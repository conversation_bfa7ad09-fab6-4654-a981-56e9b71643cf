# Django 开发者迁移指南

本指南专为熟悉 Django ORM 的开发者设计，帮助您快速理解和使用 SQLAlchemy CRUD Plus 的 Django 风格语法。

## 🔄 语法对比

### 模型定义对比

=== "Django ORM"
    ```python
    from django.db import models
    
    class User(models.Model):
        name = models.CharField(max_length=50)
        email = models.EmailField(unique=True)
        is_active = models.BooleanField(default=True)
        created_at = models.DateTimeField(auto_now_add=True)
        
        class Meta:
            db_table = 'users'
    
    class Post(models.Model):
        title = models.CharField(max_length=200)
        content = models.TextField()
        author = models.ForeignKey(User, on_delete=models.CASCADE)
        created_at = models.DateTimeField(auto_now_add=True)
    ```

=== "SQLAlchemy + CRUD Plus"
    ```python
    from sqlalchemy import String, Text, Boolean, DateTime, ForeignKey, func
    from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
    
    class Base(DeclarativeBase):
        pass
    
    class User(Base):
        __tablename__ = 'users'
        
        id: Mapped[int] = mapped_column(primary_key=True)
        name: Mapped[str] = mapped_column(String(50))
        email: Mapped[str] = mapped_column(String(100), unique=True)
        is_active: Mapped[bool] = mapped_column(Boolean, default=True)
        created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
        
        posts: Mapped[list["Post"]] = relationship(back_populates="author")
    
    class Post(Base):
        __tablename__ = 'posts'
        
        id: Mapped[int] = mapped_column(primary_key=True)
        title: Mapped[str] = mapped_column(String(200))
        content: Mapped[str] = mapped_column(Text)
        author_id: Mapped[int] = mapped_column(ForeignKey('users.id'))
        created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
        
        author: Mapped[User] = relationship(back_populates="posts")
    ```

### 查询语法对比

#### 基础查询

=== "Django ORM"
    ```python
    # 获取所有用户
    users = User.objects.all()
    
    # 根据主键获取
    user = User.objects.get(pk=1)
    
    # 条件查询
    users = User.objects.filter(is_active=True)
    
    # 排除条件
    users = User.objects.exclude(is_active=False)
    
    # 排序
    users = User.objects.order_by('-created_at')
    
    # 分页
    users = User.objects.all()[20:40]  # LIMIT 20 OFFSET 20
    ```

=== "SQLAlchemy CRUD Plus"
    ```python
    from sqlalchemy_crud_plus import CRUDPlus
    
    user_crud = CRUDPlus(User)
    
    # 获取所有用户
    users = await user_crud.select_models(session)
    
    # 根据主键获取
    user = await user_crud.select_model(session, pk=1)
    
    # 条件查询
    users = await user_crud.select_models(session, is_active=True)
    
    # 排除条件（使用 ne 操作符）
    users = await user_crud.select_models(session, is_active__ne=False)
    
    # 排序
    users = await user_crud.select_models_order(
        session, 
        sort_columns="created_at", 
        sort_orders="desc"
    )
    
    # 分页
    users = await user_crud.select_models(session, limit=20, offset=20)
    ```

#### 复杂查询

=== "Django ORM"
    ```python
    from django.db.models import Q
    
    # 字段查找
    users = User.objects.filter(
        name__icontains='张',
        email__endswith='@gmail.com',
        created_at__gte='2024-01-01'
    )
    
    # OR 查询
    users = User.objects.filter(
        Q(name__icontains='张') | Q(email__endswith='@gmail.com')
    )
    
    # IN 查询
    users = User.objects.filter(id__in=[1, 2, 3])
    
    # 范围查询
    users = User.objects.filter(age__range=[18, 65])
    ```

=== "SQLAlchemy CRUD Plus"
    ```python
    # 字段查找（Django 风格语法！）
    users = await user_crud.select_models(
        session,
        name__ilike='%张%',           # icontains -> ilike
        email__endswith='@gmail.com',
        created_at__gte='2024-01-01'
    )
    
    # OR 查询
    users = await user_crud.select_models(
        session,
        __or__={
            'name__ilike': '%张%',
            'email__endswith': '@gmail.com'
        }
    )
    
    # IN 查询
    users = await user_crud.select_models(session, id__in=[1, 2, 3])
    
    # 范围查询
    users = await user_crud.select_models(session, age__between=[18, 65])
    ```

#### 关系查询

=== "Django ORM"
    ```python
    # 预加载关系（select_related 用于 ForeignKey）
    users = User.objects.select_related('profile').all()
    
    # 预加载关系（prefetch_related 用于反向关系）
    users = User.objects.prefetch_related('posts').all()
    
    # 跨关系查询
    users = User.objects.filter(posts__title__icontains='Python')
    
    # 注解和聚合
    from django.db.models import Count
    users = User.objects.annotate(post_count=Count('posts'))
    ```

=== "SQLAlchemy CRUD Plus"
    ```python
    # 预加载关系
    users = await user_crud.select_models(
        session,
        load_strategies={
            'profile': 'joinedload',    # 类似 select_related
            'posts': 'selectinload'     # 类似 prefetch_related
        }
    )
    
    # 跨关系查询（使用 JOIN）
    users = await user_crud.select_models(
        session,
        join_conditions=['posts'],
        # 这里需要直接使用 SQLAlchemy 语法进行跨表查询
        # 或者在 Post 模型上查询然后预加载 author
    )
    
    # 聚合查询需要手动实现
    users_with_posts = await user_crud.select_models(
        session,
        load_strategies=['posts']
    )
    # 在 Python 中计算
    for user in users_with_posts:
        user.post_count = len(user.posts)
    ```

## 🔧 Django 风格的 CRUD 封装

为了让 Django 开发者更容易上手，我们可以创建一个 Django 风格的封装：

```python
# django_style_crud.py
from typing import Optional, List, Any, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_crud_plus import CRUDPlus


class DjangoStyleManager:
    """Django 风格的模型管理器"""
    
    def __init__(self, model):
        self.model = model
        self.crud = CRUDPlus(model)
    
    async def all(self, session: AsyncSession) -> List:
        """获取所有记录"""
        return await self.crud.select_models(session)
    
    async def get(self, session: AsyncSession, **kwargs) -> Optional[Any]:
        """获取单个记录"""
        if 'pk' in kwargs:
            return await self.crud.select_model(session, pk=kwargs['pk'])
        return await self.crud.select_model_by_column(session, **kwargs)
    
    async def filter(self, session: AsyncSession, **kwargs) -> List:
        """过滤查询"""
        return await self.crud.select_models(session, **kwargs)
    
    async def exclude(self, session: AsyncSession, **kwargs) -> List:
        """排除查询"""
        # 将所有条件转换为 __ne
        exclude_kwargs = {}
        for key, value in kwargs.items():
            if '__' not in key:
                exclude_kwargs[f"{key}__ne"] = value
            else:
                # 复杂操作符需要特殊处理
                exclude_kwargs[key] = value
        return await self.crud.select_models(session, **exclude_kwargs)
    
    async def order_by(
        self, 
        session: AsyncSession, 
        *fields, 
        **kwargs
    ) -> List:
        """排序查询"""
        sort_columns = []
        sort_orders = []
        
        for field in fields:
            if field.startswith('-'):
                sort_columns.append(field[1:])
                sort_orders.append('desc')
            else:
                sort_columns.append(field)
                sort_orders.append('asc')
        
        return await self.crud.select_models_order(
            session,
            sort_columns=sort_columns,
            sort_orders=sort_orders,
            **kwargs
        )
    
    async def count(self, session: AsyncSession, **kwargs) -> int:
        """统计记录数"""
        return await self.crud.count(session, **kwargs)
    
    async def exists(self, session: AsyncSession, **kwargs) -> bool:
        """检查是否存在"""
        return await self.crud.exists(session, **kwargs)
    
    async def create(self, session: AsyncSession, **kwargs) -> Any:
        """创建记录"""
        from pydantic import BaseModel, create_model
        
        # 动态创建 Pydantic 模型
        CreateModel = create_model(
            f'{self.model.__name__}Create',
            **{k: (type(v), v) for k, v in kwargs.items()}
        )
        
        create_data = CreateModel(**kwargs)
        return await self.crud.create_model(session, create_data)
    
    async def bulk_create(self, session: AsyncSession, objs: List[Dict]) -> List:
        """批量创建"""
        return await self.crud.create_models_by_dict(session, objs)
    
    async def update(self, session: AsyncSession, pk: Any, **kwargs) -> int:
        """更新记录"""
        return await self.crud.update_model(session, pk=pk, obj=kwargs)
    
    async def delete(self, session: AsyncSession, pk: Any) -> int:
        """删除记录"""
        return await self.crud.delete_model(session, pk=pk)


class DjangoStyleModel:
    """Django 风格的模型基类"""
    
    @classmethod
    def get_manager(cls):
        if not hasattr(cls, '_manager'):
            cls._manager = DjangoStyleManager(cls)
        return cls._manager
    
    @property
    def objects(self):
        return self.get_manager()


# 使用示例
class User(Base, DjangoStyleModel):
    __tablename__ = 'users'
    
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    email: Mapped[str] = mapped_column(String(100), unique=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)


# Django 风格的使用方式
async def django_style_usage(session: AsyncSession):
    user_manager = User.get_manager()
    
    # 获取所有用户
    users = await user_manager.all(session)
    
    # 条件查询
    active_users = await user_manager.filter(session, is_active=True)
    
    # 排序
    users = await user_manager.order_by(session, '-created_at', name='张三')
    
    # 创建用户
    user = await user_manager.create(
        session,
        name='新用户',
        email='<EMAIL>'
    )
    
    # 更新用户
    await user_manager.update(session, pk=user.id, name='更新后的名字')
```

## 📝 字段查找对照表

| Django 查找 | SQLAlchemy CRUD Plus | 说明 |
|-------------|---------------------|------|
| `field__exact` | `field__eq` | 精确匹配 |
| `field__iexact` | `field__eq` + 大小写处理 | 不区分大小写精确匹配 |
| `field__contains` | `field__like='%value%'` | 包含 |
| `field__icontains` | `field__ilike='%value%'` | 不区分大小写包含 |
| `field__startswith` | `field__startswith` | 开头匹配 |
| `field__endswith` | `field__endswith` | 结尾匹配 |
| `field__gt` | `field__gt` | 大于 |
| `field__gte` | `field__ge` | 大于等于 |
| `field__lt` | `field__lt` | 小于 |
| `field__lte` | `field__le` | 小于等于 |
| `field__in` | `field__in` | 在列表中 |
| `field__range` | `field__between` | 范围查询 |
| `field__isnull=True` | `field__is=None` | 为空 |
| `field__isnull=False` | `field__is_not=None` | 不为空 |

## 🔄 迁移策略

### 1. 渐进式迁移

```python
# 第一步：保持 Django 项目，添加 SQLAlchemy 支持
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'mydb',
        # ... 其他配置
    }
}

# 添加 SQLAlchemy 配置
SQLALCHEMY_DATABASE_URL = "postgresql+asyncpg://user:pass@localhost/mydb"

# 第二步：创建 SQLAlchemy 模型（与 Django 模型并存）
# sqlalchemy_models.py
class User(Base):
    __tablename__ = 'auth_user'  # 使用 Django 的表名
    # ... 字段定义

# 第三步：在新功能中使用 SQLAlchemy CRUD Plus
# views.py
async def new_api_view(request):
    async with get_session() as session:
        users = await user_crud.select_models(session, is_active=True)
        return JsonResponse({'users': [...]})

# 第四步：逐步迁移现有功能
```

### 2. 数据迁移

```python
# migrate_data.py
import asyncio
from django.core.management.base import BaseCommand
from myapp.models import User as DjangoUser
from sqlalchemy_models import User as SQLAlchemyUser

class Command(BaseCommand):
    async def migrate_users(self):
        """迁移用户数据"""
        django_users = DjangoUser.objects.all()
        
        async with get_session() as session:
            for django_user in django_users:
                # 检查是否已存在
                existing = await user_crud.select_model(
                    session, 
                    pk=django_user.id
                )
                
                if not existing:
                    user_data = UserCreate(
                        id=django_user.id,
                        name=django_user.username,
                        email=django_user.email,
                        is_active=django_user.is_active
                    )
                    await user_crud.create_model(session, user_data)
            
            await session.commit()
```

## 🎯 最佳实践

### 1. 保持一致性

```python
# 创建统一的查询接口
class UnifiedUserService:
    def __init__(self):
        self.crud = CRUDPlus(User)
    
    async def get_active_users(self, session, search=None):
        """获取活跃用户（Django 风格的方法名）"""
        filters = {'is_active': True}
        if search:
            filters['__or__'] = {
                'name__ilike': f'%{search}%',
                'email__ilike': f'%{search}%'
            }
        
        return await self.crud.select_models(session, **filters)
```

### 2. 错误处理

```python
from sqlalchemy_crud_plus.errors import MultipleResultsError

async def get_user_by_email(session, email):
    """Django 风格的错误处理"""
    try:
        return await user_crud.select_model_by_column(session, email=email)
    except MultipleResultsError:
        # 类似 Django 的 MultipleObjectsReturned
        raise ValueError(f"Multiple users found with email: {email}")
    except Exception as e:
        # 类似 Django 的 DoesNotExist
        return None
```

### 3. 分页支持

```python
class Paginator:
    """Django 风格的分页器"""
    
    def __init__(self, crud, session, per_page=20):
        self.crud = crud
        self.session = session
        self.per_page = per_page
    
    async def page(self, page_number, **filters):
        offset = (page_number - 1) * self.per_page
        
        items = await self.crud.select_models(
            self.session,
            limit=self.per_page,
            offset=offset,
            **filters
        )
        
        total = await self.crud.count(self.session, **filters)
        
        return {
            'items': items,
            'total': total,
            'page': page_number,
            'per_page': self.per_page,
            'total_pages': (total + self.per_page - 1) // self.per_page
        }

# 使用
paginator = Paginator(user_crud, session)
page_data = await paginator.page(1, is_active=True)
```

通过这个迁移指南，Django 开发者可以快速理解 SQLAlchemy CRUD Plus 的使用方式，并逐步迁移现有项目。

# 最佳实践指南

本指南汇总了在生产环境中使用 SQLAlchemy CRUD Plus 的最佳实践和经验总结。

## 🏗️ 项目结构设计

### 推荐的目录结构

```
your_project/
├── app/
│   ├── models/          # 数据模型定义
│   │   ├── __init__.py
│   │   ├── base.py      # 基础模型类
│   │   ├── user.py      # 用户相关模型
│   │   └── post.py      # 文章相关模型
│   ├── schemas/         # Pydantic 模式定义
│   │   ├── __init__.py
│   │   ├── user.py      # 用户相关模式
│   │   └── post.py      # 文章相关模式
│   ├── crud/           # CRUD 操作层
│   │   ├── __init__.py
│   │   ├── base.py     # 基础 CRUD 类
│   │   ├── user.py     # 用户 CRUD 操作
│   │   └── post.py     # 文章 CRUD 操作
│   ├── api/            # API 路由层
│   ├── core/           # 核心配置
│   │   ├── database.py # 数据库配置
│   │   └── config.py   # 应用配置
│   └── main.py         # 应用入口
```

### 基础模型设计

```python
# app/models/base.py
from datetime import datetime
from sqlalchemy import DateTime, func
from sqlalchemy.ext.declarative import DeclarativeBase
from sqlalchemy.orm import Mapped, mapped_column


class Base(DeclarativeBase):
    """基础模型类，包含通用字段"""
    pass


class TimestampMixin:
    """时间戳混入类"""
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    deleted_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )
    is_deleted: Mapped[bool] = mapped_column(
        default=False,
        comment="是否已删除"
    )
```

### 基础 CRUD 类设计

```python
# app/crud/base.py
from typing import Generic, TypeVar, Type, Optional, List, Any, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_crud_plus import CRUDPlus
from pydantic import BaseModel

ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseCRUD(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.crud = CRUDPlus(model)
        self.model = model

    async def create(
        self,
        session: AsyncSession,
        obj_in: CreateSchemaType,
        **kwargs
    ) -> ModelType:
        """创建记录"""
        return await self.crud.create_model(session, obj_in, **kwargs)

    async def get(
        self,
        session: AsyncSession,
        pk: Any,
        **kwargs
    ) -> Optional[ModelType]:
        """根据主键获取记录"""
        return await self.crud.select_model(session, pk=pk, **kwargs)

    async def get_multi(
        self,
        session: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        **kwargs
    ) -> List[ModelType]:
        """获取多条记录"""
        return await self.crud.select_models(
            session, 
            offset=skip, 
            limit=limit, 
            **kwargs
        )

    async def update(
        self,
        session: AsyncSession,
        pk: Any,
        obj_in: UpdateSchemaType | Dict[str, Any],
        **kwargs
    ) -> int:
        """更新记录"""
        return await self.crud.update_model(session, pk=pk, obj=obj_in, **kwargs)

    async def delete(
        self,
        session: AsyncSession,
        pk: Any,
        **kwargs
    ) -> int:
        """删除记录"""
        return await self.crud.delete_model(session, pk=pk, **kwargs)

    async def count(
        self,
        session: AsyncSession,
        **kwargs
    ) -> int:
        """统计记录数"""
        return await self.crud.count(session, **kwargs)

    async def exists(
        self,
        session: AsyncSession,
        **kwargs
    ) -> bool:
        """检查记录是否存在"""
        return await self.crud.exists(session, **kwargs)
```

## 🔧 配置管理

### 数据库配置

```python
# app/core/database.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from app.core.config import settings


class DatabaseManager:
    def __init__(self):
        self.engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DEBUG,
            poolclass=NullPool if settings.TESTING else None,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
        self.async_session = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        async with self.async_session() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()


# 全局数据库管理器实例
db_manager = DatabaseManager()

# 依赖注入函数
async def get_db() -> AsyncSession:
    async for session in db_manager.get_session():
        yield session
```

## 📝 模式设计原则

### Pydantic 模式最佳实践

```python
# app/schemas/user.py
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, ConfigDict


class UserBase(BaseModel):
    """用户基础模式"""
    name: str = Field(..., min_length=1, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """用户创建模式"""
    password: str = Field(..., min_length=8, description="密码")


class UserUpdate(BaseModel):
    """用户更新模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None


class UserInDB(UserBase):
    """数据库中的用户模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: datetime


class UserResponse(UserInDB):
    """用户响应模式"""
    # 排除敏感字段
    pass


class UserWithPosts(UserResponse):
    """包含文章的用户模式"""
    posts: List['PostResponse'] = []
```

## 🚀 性能优化策略

### 1. 查询优化

```python
# ✅ 好的做法：预加载关系数据
users = await user_crud.select_models(
    session,
    load_strategies={
        'posts': 'selectinload',  # 一对多关系
        'profile': 'joinedload',  # 一对一关系
    },
    limit=20
)

# ❌ 避免：N+1 查询问题
users = await user_crud.select_models(session, limit=20)
for user in users:
    print(len(user.posts))  # 每次访问都会触发查询
```

### 2. 批量操作优化

```python
# ✅ 好的做法：使用批量操作
users_data = [UserCreate(...) for _ in range(1000)]
users = await user_crud.create_models(session, users_data)

# ❌ 避免：循环单个操作
for user_data in users_data:
    await user_crud.create_model(session, user_data)
```

### 3. 事务管理优化

```python
# ✅ 好的做法：合理的事务范围
async with session.begin():
    user = await user_crud.create_model(session, user_data, flush=True)
    profile = await profile_crud.create_model(
        session, 
        ProfileCreate(user_id=user.id, ...)
    )

# ❌ 避免：过长的事务
async with session.begin():
    # 大量操作...
    time.sleep(10)  # 长时间操作
    # 更多操作...
```

## 🛡️ 错误处理策略

### 统一错误处理

```python
# app/core/exceptions.py
from sqlalchemy_crud_plus.errors import (
    ModelColumnError,
    SelectOperatorError,
    MultipleResultsError
)


class BusinessException(Exception):
    """业务异常基类"""
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(message)


class UserNotFoundError(BusinessException):
    """用户不存在异常"""
    def __init__(self, user_id: int):
        super().__init__(f"用户 {user_id} 不存在", "USER_NOT_FOUND")


# 在 CRUD 操作中使用
async def get_user_or_404(session: AsyncSession, user_id: int):
    user = await user_crud.select_model(session, pk=user_id)
    if not user:
        raise UserNotFoundError(user_id)
    return user
```

## 📊 监控和日志

### 查询监控

```python
# app/core/monitoring.py
import time
import logging
from functools import wraps

logger = logging.getLogger(__name__)


def monitor_query(operation_name: str):
    """查询监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{operation_name} 执行成功，耗时: {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{operation_name} 执行失败，耗时: {duration:.3f}s，错误: {e}")
                raise
        return wrapper
    return decorator


# 使用示例
class UserCRUD(BaseCRUD[User, UserCreate, UserUpdate]):
    @monitor_query("获取用户列表")
    async def get_users_with_posts(self, session: AsyncSession):
        return await self.crud.select_models(
            session,
            load_strategies=['posts']
        )
```

## 🧪 测试策略

### 单元测试最佳实践

```python
# tests/test_user_crud.py
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.crud.user import user_crud
from app.schemas.user import UserCreate, UserUpdate


@pytest.mark.asyncio
async def test_create_user(db_session: AsyncSession):
    """测试创建用户"""
    user_data = UserCreate(
        name="测试用户",
        email="<EMAIL>",
        password="password123"
    )
    
    user = await user_crud.create(db_session, user_data)
    
    assert user.name == "测试用户"
    assert user.email == "<EMAIL>"
    assert user.id is not None


@pytest.mark.asyncio
async def test_get_user_with_posts(db_session: AsyncSession):
    """测试获取用户及其文章"""
    # 准备测试数据
    user = await create_test_user(db_session)
    await create_test_posts(db_session, user.id)
    
    # 执行查询
    result = await user_crud.crud.select_model(
        db_session,
        pk=user.id,
        load_strategies=['posts']
    )
    
    assert result is not None
    assert len(result.posts) > 0
```

## 📈 部署和运维

### 生产环境配置

```python
# app/core/config.py
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    
    # 应用配置
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # 性能配置
    QUERY_TIMEOUT: int = 30
    MAX_PAGE_SIZE: int = 1000
    DEFAULT_PAGE_SIZE: int = 20
    
    class Config:
        env_file = ".env"


settings = Settings()
```

这些最佳实践将帮助您构建高质量、可维护的应用程序。接下来的章节将深入探讨具体的实现细节。

# CRUDPlus API 参考

`CRUDPlus` 类是 SQLAlchemy CRUD Plus 的核心类，提供了完整的 CRUD 操作和关系查询功能。

## 类概览

```python
from sqlalchemy_crud_plus import CRUDPlus
from typing import TypeVar, Generic

ModelType = TypeVar('ModelType')

class CRUDPlus(Generic[ModelType]):
    def __init__(self, model: type[ModelType]) -> None: ...
```

## 构造函数

### `__init__(model)`

创建 CRUDPlus 实例。

**参数：**
- `model` (type[Model]): SQLAlchemy 模型类

**示例：**
```python
from sqlalchemy_crud_plus import CRUDPlus
from models import User

user_crud = CRUDPlus(User)
```

## 创建操作 (Create)

### `create_model()`

创建单个模型实例。

```python
async def create_model(
    self,
    session: AsyncSession,
    obj: CreateSchema,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> ModelType
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `obj` (CreateSchema): Pydantic 创建模式实例
- `flush` (bool): 是否立即刷新到数据库（获取主键）
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 额外的模型字段数据

**返回：**
- `ModelType`: 创建的模型实例

**示例：**
```python
user_data = UserCreate(name="张三", email="<EMAIL>")
user = await user_crud.create_model(session, user_data)

# 使用 flush 获取主键
user = await user_crud.create_model(session, user_data, flush=True)

# 立即提交
user = await user_crud.create_model(session, user_data, commit=True)

# 添加额外字段
user = await user_crud.create_model(
    session,
    user_data,
    created_by="admin"
)
```

### `create_models()`

批量创建多个模型实例。

```python
async def create_models(
    self,
    session: AsyncSession,
    objs: list[CreateSchema],
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> list[ModelType]
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `objs` (list[CreateSchema]): Pydantic 创建模式实例列表
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 应用到所有实例的额外字段

**返回：**
- `list[ModelType]`: 创建的模型实例列表

**示例：**
```python
users_data = [
    UserCreate(name="用户1", email="<EMAIL>"),
    UserCreate(name="用户2", email="<EMAIL>")
]
users = await user_crud.create_models(session, users_data)
```

### `create_models_by_dict()`

使用字典数据批量创建模型实例（高性能）。

```python
async def create_models_by_dict(
    self,
    session: AsyncSession,
    objs: list[dict],
    render_nulls: bool = False,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> list[ModelType]
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `objs` (list[dict]): 字典数据列表
- `render_nulls` (bool): 是否渲染 NULL 值
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 应用到所有实例的额外字段

**返回：**
- `list[ModelType]`: 创建的模型实例列表

**示例：**
```python
users_data = [
    {"name": "用户1", "email": "<EMAIL>"},
    {"name": "用户2", "email": "<EMAIL>"}
]
users = await user_crud.create_models_by_dict(session, users_data)
```

## 查询操作 (Read)

### `select_model()`

根据主键查询单个模型实例。

```python
async def select_model(
    self,
    session: AsyncSession,
    pk: Any,
    *whereclause,
    load_options: LoadOptions = None,
    load_strategies: LoadStrategies = None,
    join_conditions: JoinConditions = None,
    **kwargs
) -> ModelType | None
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `pk` (Any): 主键值（复合主键使用元组）
- `*whereclause`: 额外的 WHERE 条件
- `load_options` (LoadOptions): 原生 SQLAlchemy 加载选项
- `load_strategies` (LoadStrategies): 简化的加载策略
- `join_conditions` (JoinConditions): JOIN 条件
- `**kwargs`: 额外的过滤条件

**返回：**
- `ModelType | None`: 查询到的模型实例或 None

**示例：**
```python
# 基础查询
user = await user_crud.select_model(session, pk=1)

# 复合主键
user_role = await user_role_crud.select_model(session, pk=(1, 2))

# 预加载关系
user = await user_crud.select_model(
    session,
    pk=1,
    load_strategies=['posts', 'profile']
)

# 额外条件
user = await user_crud.select_model(
    session,
    pk=1,
    is_active=True
)
```

### `select_model_by_column()`

根据列条件查询单个模型实例。

```python
async def select_model_by_column(
    self,
    session: AsyncSession,
    *whereclause,
    load_options: LoadOptions = None,
    load_strategies: LoadStrategies = None,
    join_conditions: JoinConditions = None,
    **kwargs
) -> ModelType | None
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `*whereclause`: WHERE 条件表达式
- `load_options` (LoadOptions): 原生 SQLAlchemy 加载选项
- `load_strategies` (LoadStrategies): 简化的加载策略
- `join_conditions` (JoinConditions): JOIN 条件
- `**kwargs`: 过滤条件

**返回：**
- `ModelType | None`: 查询到的模型实例或 None

**异常：**
- `MultipleResultsError`: 当查询返回多个结果时抛出

**示例：**
```python
# 基础查询
user = await user_crud.select_model_by_column(
    session,
    email="<EMAIL>"
)

# 复杂条件
user = await user_crud.select_model_by_column(
    session,
    User.is_active == True,
    name__like="%张%"
)
```

### `select_models()`

查询多个模型实例。

```python
async def select_models(
    self,
    session: AsyncSession,
    *whereclause,
    load_options: LoadOptions = None,
    load_strategies: LoadStrategies = None,
    join_conditions: JoinConditions = None,
    limit: int = None,
    offset: int = None,
    **kwargs
) -> list[ModelType]
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `*whereclause`: WHERE 条件表达式
- `load_options` (LoadOptions): 原生 SQLAlchemy 加载选项
- `load_strategies` (LoadStrategies): 简化的加载策略
- `join_conditions` (JoinConditions): JOIN 条件
- `limit` (int): 限制返回记录数
- `offset` (int): 跳过记录数（分页）
- `**kwargs`: 过滤条件

**返回：**
- `list[ModelType]`: 查询到的模型实例列表

**示例：**
```python
# 基础查询
users = await user_crud.select_models(session)

# 条件查询
users = await user_crud.select_models(
    session,
    is_active=True,
    name__like="%张%"
)

# 分页查询
users = await user_crud.select_models(
    session,
    limit=20,
    offset=40
)

# 预加载关系
users = await user_crud.select_models(
    session,
    load_strategies=['posts', 'profile'],
    limit=10
)

# JOIN 查询
users = await user_crud.select_models(
    session,
    join_conditions=['posts'],
    is_active=True
)
```

### `select_models_order()`

带排序的查询多个模型实例。

```python
async def select_models_order(
    self,
    session: AsyncSession,
    sort_columns: SortColumns,
    sort_orders: SortOrders = None,
    *whereclause,
    load_options: LoadOptions = None,
    load_strategies: LoadStrategies = None,
    join_conditions: JoinConditions = None,
    limit: int = None,
    offset: int = None,
    **kwargs
) -> list[ModelType]
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `sort_columns` (SortColumns): 排序字段（字符串或列表）
- `sort_orders` (SortOrders): 排序方向（'asc'/'desc' 或列表）
- 其他参数同 `select_models()`

**返回：**
- `list[ModelType]`: 排序后的模型实例列表

**示例：**
```python
# 单字段排序
users = await user_crud.select_models_order(
    session,
    sort_columns="created_at",
    sort_orders="desc"
)

# 多字段排序
users = await user_crud.select_models_order(
    session,
    sort_columns=["name", "created_at"],
    sort_orders=["asc", "desc"]
)

# 结合其他条件
users = await user_crud.select_models_order(
    session,
    sort_columns="created_at",
    sort_orders="desc",
    is_active=True,
    limit=10
)
```

### `count()`

统计符合条件的记录数量。

```python
async def count(
    self,
    session: AsyncSession,
    *whereclause,
    **kwargs
) -> int
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `*whereclause`: WHERE 条件表达式
- `**kwargs`: 过滤条件

**返回：**
- `int`: 记录数量

**示例：**
```python
# 总数统计
total = await user_crud.count(session)

# 条件统计
active_count = await user_crud.count(session, is_active=True)

# 复杂条件统计
recent_count = await user_crud.count(
    session,
    User.created_at > datetime.now() - timedelta(days=30),
    is_active=True
)
```

### `exists()`

检查符合条件的记录是否存在。

```python
async def exists(
    self,
    session: AsyncSession,
    *whereclause,
    **kwargs
) -> bool
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `*whereclause`: WHERE 条件表达式
- `**kwargs`: 过滤条件

**返回：**
- `bool`: 是否存在符合条件的记录

**示例：**
```python
# 检查邮箱是否存在
exists = await user_crud.exists(session, email="<EMAIL>")

# 检查活跃用户是否存在
has_active = await user_crud.exists(session, is_active=True)
```

## 更新操作 (Update)

### `update_model()`

根据主键更新模型实例。

```python
async def update_model(
    self,
    session: AsyncSession,
    pk: Any,
    obj: UpdateSchema | dict,
    *whereclause,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> int
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `pk` (Any): 主键值
- `obj` (UpdateSchema | dict): 更新数据
- `*whereclause`: 额外的 WHERE 条件
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 额外的更新字段

**返回：**
- `int`: 更新的记录数量

**示例：**
```python
# 使用 Pydantic 模式更新
update_data = UserUpdate(name="新名称")
count = await user_crud.update_model(session, pk=1, obj=update_data)

# 使用字典更新
count = await user_crud.update_model(
    session,
    pk=1,
    obj={"name": "新名称", "is_active": True}
)

# 复合主键更新
count = await user_role_crud.update_model(
    session,
    pk=(1, 2),
    obj={"assigned_at": datetime.now()}
)

# 额外条件
count = await user_crud.update_model(
    session,
    pk=1,
    obj={"name": "新名称"},
    User.is_active == True  # 只有活跃用户才能更新
)
```

### `update_model_by_column()`

根据列条件更新模型实例。

```python
async def update_model_by_column(
    self,
    session: AsyncSession,
    obj: UpdateSchema | dict,
    *whereclause,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> int
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `obj` (UpdateSchema | dict): 更新数据
- `*whereclause`: WHERE 条件表达式
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 过滤条件

**返回：**
- `int`: 更新的记录数量

**示例：**
```python
# 条件批量更新
count = await user_crud.update_model_by_column(
    session,
    obj={"is_active": False},
    name__like="%test%"
)

# 复杂条件更新
count = await user_crud.update_model_by_column(
    session,
    obj={"last_login": datetime.now()},
    User.created_at < datetime.now() - timedelta(days=30),
    is_active=True
)
```

## 删除操作 (Delete)

### `delete_model()`

根据主键删除模型实例。

```python
async def delete_model(
    self,
    session: AsyncSession,
    pk: Any,
    *whereclause,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> int
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `pk` (Any): 主键值
- `*whereclause`: 额外的 WHERE 条件
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 额外的过滤条件

**返回：**
- `int`: 删除的记录数量

**示例：**
```python
# 基础删除
count = await user_crud.delete_model(session, pk=1)

# 复合主键删除
count = await user_role_crud.delete_model(session, pk=(1, 2))

# 额外条件
count = await user_crud.delete_model(
    session,
    pk=1,
    User.is_active == False  # 只删除非活跃用户
)
```

### `delete_model_by_column()`

根据列条件删除模型实例。

```python
async def delete_model_by_column(
    self,
    session: AsyncSession,
    *whereclause,
    flush: bool = False,
    commit: bool = False,
    **kwargs
) -> int
```

**参数：**
- `session` (AsyncSession): SQLAlchemy 异步会话
- `*whereclause`: WHERE 条件表达式
- `flush` (bool): 是否立即刷新到数据库
- `commit` (bool): 是否立即提交事务
- `**kwargs`: 过滤条件

**返回：**
- `int`: 删除的记录数量

**示例：**
```python
# 条件删除
count = await user_crud.delete_model_by_column(
    session,
    is_active=False
)

# 复杂条件删除
count = await user_crud.delete_model_by_column(
    session,
    User.created_at < datetime.now() - timedelta(days=365),
    is_active=False
)
```

## 参数详解

### LoadStrategies

关系加载策略配置，支持以下格式：

```python
# 类型定义
LoadStrategies = list[str] | dict[str, LoadingStrategy]

# 支持的策略
LoadingStrategy = Literal[
    'selectinload',    # SELECT IN 加载（推荐用于一对多）
    'joinedload',      # JOIN 加载（推荐用于一对一）
    'subqueryload',    # 子查询加载
    'contains_eager',  # 与显式 JOIN 一起使用
    'raiseload',       # 禁止懒加载（抛出异常）
    'noload',          # 不加载关系
    'immediateload',   # 立即加载
    'lazyload'         # 懒加载（默认）
]
```

**示例：**
```python
# 列表格式（使用默认策略）
load_strategies = ['posts', 'profile']

# 字典格式（指定策略）
load_strategies = {
    'posts': 'selectinload',
    'profile': 'joinedload',
    'roles': 'subqueryload'
}

# 嵌套关系
load_strategies = {
    'posts': 'selectinload',
    'posts.comments': 'selectinload',
    'posts.category': 'joinedload'
}
```

### JoinConditions

JOIN 条件配置，支持以下格式：

```python
# 类型定义
JoinConditions = list[str | JoinConfig] | dict[str, JoinType]

JoinType = Literal['inner', 'left', 'right', 'full']
```

**示例：**
```python
# 列表格式（默认 INNER JOIN）
join_conditions = ['posts', 'profile']

# 字典格式（指定 JOIN 类型）
join_conditions = {
    'posts': 'inner',
    'profile': 'left',
    'roles': 'right'
}

# 使用 JoinConfig（高级配置）
from sqlalchemy_crud_plus.types import JoinConfig

join_conditions = [
    JoinConfig(
        model=Post,
        join_on=User.id == Post.author_id,
        join_type='left'
    )
]
```

### 过滤操作符

支持的过滤操作符及其用法：

#### 比较操作符
```python
# 数值比较
users = await user_crud.select_models(
    session,
    age__gt=30,         # 大于
    age__ge=18,         # 大于等于
    age__lt=65,         # 小于
    age__le=60,         # 小于等于
    id__eq=1,           # 等于
    status__ne=0        # 不等于
)
```

#### 包含操作符
```python
# 范围查询
users = await user_crud.select_models(
    session,
    id__in=[1, 2, 3],           # 在列表中
    status__not_in=[0, -1],     # 不在列表中
    age__between=[18, 65]       # 在范围内
)
```

#### 字符串操作符
```python
# 字符串匹配
users = await user_crud.select_models(
    session,
    name__like='%张%',              # 包含
    name__not_like='%test%',        # 不包含
    name__ilike='%ADMIN%',          # 不区分大小写包含
    name__startswith='admin',       # 开头匹配
    name__endswith='@qq.com',       # 结尾匹配
    bio__contains='程序员'           # 包含文本
)
```

#### OR 条件
```python
# OR 条件查询
users = await user_crud.select_models(
    session,
    __or__={
        'name__like': '%张%',
        'email__endswith': '@gmail.com',
        'is_vip': True
    }
)
```

## 异常处理

### 内置异常类型

```python
from sqlalchemy_crud_plus.errors import (
    ModelColumnError,           # 模型字段错误
    SelectOperatorError,        # 查询操作符错误
    ColumnSortError,           # 排序字段错误
    MultipleResultsError,      # 多结果错误
    CompositePrimaryKeysError, # 复合主键错误
    LoadingStrategyError,      # 加载策略错误
    JoinConditionError         # JOIN 条件错误
)
```

### 异常处理示例

```python
try:
    user = await user_crud.select_model_by_column(
        session,
        email="<EMAIL>"
    )
except MultipleResultsError:
    # 处理多个结果的情况
    users = await user_crud.select_models(
        session,
        email="<EMAIL>"
    )
except ModelColumnError as e:
    # 处理字段不存在的情况
    print(f"字段错误: {e}")
```

## 最佳实践

### 1. 类型提示

```python
from typing import Optional, List
from sqlalchemy_crud_plus import CRUDPlus

class UserCRUD:
    def __init__(self):
        self.crud = CRUDPlus(User)

    async def get_user(self, session: AsyncSession, user_id: int) -> Optional[User]:
        return await self.crud.select_model(session, pk=user_id)

    async def get_users(self, session: AsyncSession) -> List[User]:
        return await self.crud.select_models(session)
```

### 2. 错误处理

```python
async def safe_get_user_by_email(session: AsyncSession, email: str) -> Optional[User]:
    try:
        return await user_crud.select_model_by_column(session, email=email)
    except MultipleResultsError:
        # 记录警告并返回第一个结果
        users = await user_crud.select_models(session, email=email, limit=1)
        return users[0] if users else None
```

### 3. 性能优化

```python
# 预加载关系数据避免 N+1 查询
users = await user_crud.select_models(
    session,
    load_strategies={
        'posts': 'selectinload',      # 一对多用 selectinload
        'profile': 'joinedload'       # 一对一用 joinedload
    },
    limit=20
)

# 使用 exists 而不是 count 检查存在性
if await user_crud.exists(session, email=email):
    # 处理存在的情况
    pass
```

这个完整的 API 参考文档涵盖了 CRUDPlus 类的所有方法、参数和使用示例，为开发者提供了详细的技术参考。

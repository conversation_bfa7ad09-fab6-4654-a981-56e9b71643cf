name: Release changelog

on:
  release:
    types:
      - published

jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: master

      - uses: rhysd/changelog-from-release/action@v3
        with:
          file: docs/changelog.md
          pull_request: true
          github_token: ${{ secrets.GH_TOKEN }}

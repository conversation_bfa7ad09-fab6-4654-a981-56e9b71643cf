# FastAPI 集成指南

本指南详细介绍如何将 SQLAlchemy CRUD Plus 与 FastAPI 完美集成，构建高性能的现代 Web API。

## 🚀 快速集成

### 项目结构

```
fastapi_project/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 应用入口
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py        # 配置管理
│   │   └── database.py      # 数据库配置
│   ├── models/
│   │   ├── __init__.py
│   │   └── user.py          # 数据模型
│   ├── schemas/
│   │   ├── __init__.py
│   │   └── user.py          # Pydantic 模式
│   ├── crud/
│   │   ├── __init__.py
│   │   └── user.py          # CRUD 操作
│   └── api/
│       ├── __init__.py
│       ├── deps.py          # 依赖注入
│       └── v1/
│           ├── __init__.py
│           └── users.py     # 用户 API 路由
├── requirements.txt
└── .env
```

### 基础配置

```python
# app/core/config.py
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "FastAPI CRUD Plus"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # API 配置
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS 配置
    BACKEND_CORS_ORIGINS: list[str] = []
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
```

### 数据库配置

```python
# app/core/database.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import DeclarativeBase
from app.core.config import settings


class Base(DeclarativeBase):
    pass


class DatabaseManager:
    def __init__(self):
        self.engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DEBUG,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
        
        self.async_session = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )
    
    async def get_session(self):
        async with self.async_session() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def close(self):
        await self.engine.dispose()


# 全局数据库管理器
db_manager = DatabaseManager()
```

## 📝 模型和模式定义

### 数据模型

```python
# app/models/user.py
from datetime import datetime
from sqlalchemy import String, DateTime, Boolean, func
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database import Base


class User(Base):
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    name: Mapped[str] = mapped_column(String(50), index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # 关系
    posts: Mapped[list["Post"]] = relationship(back_populates="author")
```

### Pydantic 模式

```python
# app/schemas/user.py
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, ConfigDict


class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr = Field(..., description="邮箱地址")
    name: str = Field(..., min_length=1, max_length=50, description="用户名")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """用户创建模式"""
    password: str = Field(..., min_length=8, description="密码")


class UserUpdate(BaseModel):
    """用户更新模式"""
    email: Optional[EmailStr] = None
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    is_active: Optional[bool] = None


class UserInDB(UserBase):
    """数据库用户模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    is_superuser: bool
    created_at: datetime
    updated_at: datetime


class UserResponse(UserInDB):
    """用户响应模式"""
    pass


class UserListResponse(BaseModel):
    """用户列表响应模式"""
    items: List[UserResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# 分页参数模式
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.page_size
```

## 🔧 CRUD 操作层

```python
# app/crud/user.py
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_crud_plus import CRUDPlus
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class UserCRUD:
    def __init__(self):
        self.crud = CRUDPlus(User)
    
    async def create(
        self, 
        session: AsyncSession, 
        user_in: UserCreate
    ) -> User:
        """创建用户"""
        # 这里可以添加密码哈希等逻辑
        user_data = user_in.model_dump()
        # user_data["hashed_password"] = hash_password(user_data.pop("password"))
        
        return await self.crud.create_model(session, UserCreate(**user_data))
    
    async def get(
        self, 
        session: AsyncSession, 
        user_id: int
    ) -> Optional[User]:
        """根据ID获取用户"""
        return await self.crud.select_model(session, pk=user_id)
    
    async def get_by_email(
        self, 
        session: AsyncSession, 
        email: str
    ) -> Optional[User]:
        """根据邮箱获取用户"""
        return await self.crud.select_model_by_column(session, email=email)
    
    async def get_multi(
        self,
        session: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        **filters
    ) -> List[User]:
        """获取用户列表"""
        return await self.crud.select_models(
            session,
            offset=skip,
            limit=limit,
            **filters
        )
    
    async def count(
        self,
        session: AsyncSession,
        **filters
    ) -> int:
        """统计用户数量"""
        return await self.crud.count(session, **filters)
    
    async def update(
        self,
        session: AsyncSession,
        user_id: int,
        user_in: UserUpdate
    ) -> Optional[int]:
        """更新用户"""
        update_data = user_in.model_dump(exclude_unset=True)
        if not update_data:
            return 0
        
        return await self.crud.update_model(
            session, 
            pk=user_id, 
            obj=update_data
        )
    
    async def delete(
        self,
        session: AsyncSession,
        user_id: int
    ) -> int:
        """删除用户"""
        return await self.crud.delete_model(session, pk=user_id)
    
    async def activate(
        self,
        session: AsyncSession,
        user_id: int
    ) -> int:
        """激活用户"""
        return await self.crud.update_model(
            session,
            pk=user_id,
            obj={"is_active": True}
        )
    
    async def deactivate(
        self,
        session: AsyncSession,
        user_id: int
    ) -> int:
        """停用用户"""
        return await self.crud.update_model(
            session,
            pk=user_id,
            obj={"is_active": False}
        )


# 全局 CRUD 实例
user_crud = UserCRUD()
```

## 🔗 依赖注入

```python
# app/api/deps.py
from typing import AsyncGenerator
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import db_manager
from app.crud.user import user_crud
from app.models.user import User


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    async for session in db_manager.get_session():
        yield session


async def get_current_user(
    session: AsyncSession = Depends(get_db),
    # token: str = Depends(oauth2_scheme)  # 如果需要认证
) -> User:
    """获取当前用户"""
    # 这里应该从 token 中解析用户信息
    # user_id = decode_token(token)
    user_id = 1  # 示例
    
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被停用"
        )
    
    return user


async def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user
```

## 🛣️ API 路由

```python
# app/api/v1/users.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_user, get_current_active_superuser
from app.crud.user import user_crud
from app.schemas.user import (
    UserCreate, 
    UserUpdate, 
    UserResponse, 
    UserListResponse,
    PaginationParams
)
from app.models.user import User

router = APIRouter()


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_in: UserCreate,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """创建用户"""
    # 检查邮箱是否已存在
    existing_user = await user_crud.get_by_email(session, user_in.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    user = await user_crud.create(session, user_in)
    return user


@router.get("/", response_model=UserListResponse)
async def get_users(
    pagination: PaginationParams = Depends(),
    is_active: bool = Query(None, description="是否激活"),
    search: str = Query(None, description="搜索关键词"),
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """获取用户列表"""
    # 构建过滤条件
    filters = {}
    if is_active is not None:
        filters["is_active"] = is_active
    
    if search:
        filters["__or__"] = {
            "name__like": f"%{search}%",
            "email__like": f"%{search}%"
        }
    
    # 获取用户列表
    users = await user_crud.get_multi(
        session,
        skip=pagination.offset,
        limit=pagination.page_size,
        **filters
    )
    
    # 获取总数
    total = await user_crud.count(session, **filters)
    
    return UserListResponse(
        items=users,
        total=total,
        page=pagination.page,
        page_size=pagination.page_size,
        total_pages=(total + pagination.page_size - 1) // pagination.page_size
    )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户详情"""
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 权限检查：只能查看自己的信息或管理员可以查看所有
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    return user


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新用户"""
    # 权限检查
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    # 检查用户是否存在
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 如果更新邮箱，检查是否重复
    if user_in.email and user_in.email != user.email:
        existing_user = await user_crud.get_by_email(session, user_in.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被使用"
            )
    
    # 执行更新
    updated_count = await user_crud.update(session, user_id, user_in)
    if updated_count == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="更新失败"
        )
    
    # 返回更新后的用户
    updated_user = await user_crud.get(session, user_id)
    return updated_user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """删除用户"""
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    deleted_count = await user_crud.delete(session, user_id)
    if deleted_count == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="删除失败"
        )


@router.post("/{user_id}/activate", response_model=UserResponse)
async def activate_user(
    user_id: int,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """激活用户"""
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    await user_crud.activate(session, user_id)
    updated_user = await user_crud.get(session, user_id)
    return updated_user


@router.post("/{user_id}/deactivate", response_model=UserResponse)
async def deactivate_user(
    user_id: int,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """停用用户"""
    user = await user_crud.get(session, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    await user_crud.deactivate(session, user_id)
    updated_user = await user_crud.get(session, user_id)
    return updated_user
```

## 🚀 FastAPI 应用配置

### 主应用文件

```python
# app/main.py
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import db_manager
from app.api.v1 import users
from sqlalchemy_crud_plus.errors import (
    ModelColumnError,
    SelectOperatorError,
    MultipleResultsError
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 应用启动中...")
    yield
    # 关闭时执行
    print("🛑 应用关闭中...")
    await db_manager.close()


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    description="基于 SQLAlchemy CRUD Plus 的现代 Web API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS 中间件
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.example.com"]
)


# 全局异常处理器
@app.exception_handler(ModelColumnError)
async def model_column_error_handler(request: Request, exc: ModelColumnError):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "error": "模型字段错误",
            "detail": str(exc),
            "type": "MODEL_COLUMN_ERROR"
        }
    )


@app.exception_handler(SelectOperatorError)
async def select_operator_error_handler(request: Request, exc: SelectOperatorError):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "error": "查询操作符错误",
            "detail": str(exc),
            "type": "SELECT_OPERATOR_ERROR"
        }
    )


@app.exception_handler(MultipleResultsError)
async def multiple_results_error_handler(request: Request, exc: MultipleResultsError):
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content={
            "error": "查询返回多个结果",
            "detail": str(exc),
            "type": "MULTIPLE_RESULTS_ERROR"
        }
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "app_name": settings.APP_NAME
    }


# 注册路由
app.include_router(
    users.router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["用户管理"]
)


# 根路径
@app.get("/")
async def root():
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.VERSION,
        "docs": "/docs"
    }
```

## 🔒 认证和授权

### JWT 认证实现

```python
# app/core/security.py
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from app.core.config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT 配置
ALGORITHM = "HS256"


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return {"user_id": user_id}
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)
```

### 认证路由

```python
# app/api/v1/auth.py
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db
from app.core.config import settings
from app.core.security import (
    create_access_token,
    verify_password,
    get_password_hash
)
from app.crud.user import user_crud
from app.schemas.auth import Token, UserLogin
from app.schemas.user import UserCreate, UserResponse

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_in: UserCreate,
    session: AsyncSession = Depends(get_db)
):
    """用户注册"""
    # 检查邮箱是否已存在
    existing_user = await user_crud.get_by_email(session, user_in.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )

    # 创建用户（密码会在 CRUD 层进行哈希处理）
    user = await user_crud.create(session, user_in)
    return user


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: AsyncSession = Depends(get_db)
):
    """用户登录"""
    # 验证用户
    user = await user_crud.get_by_email(session, form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被停用"
        )

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }
```

## 🧪 测试配置

### 测试数据库配置

```python
# tests/conftest.py
import pytest
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.database import Base
from app.main import app
from app.api.deps import get_db

# 测试数据库 URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=True
)

# 创建测试会话
TestSessionLocal = async_sessionmaker(
    bind=test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_database():
    """设置测试数据库"""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
async def db_session(setup_database) -> AsyncGenerator[AsyncSession, None]:
    """获取测试数据库会话"""
    async with TestSessionLocal() as session:
        yield session


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """覆盖数据库依赖"""
    async def _override_get_db():
        yield db_session

    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()
```

### API 测试示例

```python
# tests/test_users_api.py
import pytest
from httpx import AsyncClient
from fastapi import status

from app.main import app
from app.schemas.user import UserCreate


@pytest.mark.asyncio
async def test_create_user(override_get_db):
    """测试创建用户 API"""
    user_data = {
        "email": "<EMAIL>",
        "name": "测试用户",
        "password": "password123"
    }

    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post("/api/v1/users/", json=user_data)

    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["name"] == user_data["name"]
    assert "id" in data


@pytest.mark.asyncio
async def test_get_users(override_get_db):
    """测试获取用户列表 API"""
    # 首先创建一些测试用户
    users_data = [
        {"email": f"user{i}@example.com", "name": f"用户{i}", "password": "password123"}
        for i in range(5)
    ]

    async with AsyncClient(app=app, base_url="http://test") as client:
        # 创建用户
        for user_data in users_data:
            await client.post("/api/v1/users/", json=user_data)

        # 获取用户列表
        response = await client.get("/api/v1/users/")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert len(data["items"]) == 5
    assert data["total"] == 5


@pytest.mark.asyncio
async def test_get_user_by_id(override_get_db):
    """测试根据ID获取用户 API"""
    user_data = {
        "email": "<EMAIL>",
        "name": "测试用户",
        "password": "password123"
    }

    async with AsyncClient(app=app, base_url="http://test") as client:
        # 创建用户
        create_response = await client.post("/api/v1/users/", json=user_data)
        user_id = create_response.json()["id"]

        # 获取用户
        response = await client.get(f"/api/v1/users/{user_id}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["name"] == user_data["name"]
```

## 📊 监控和日志

### 请求日志中间件

```python
# app/middleware/logging.py
import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()

        # 记录请求信息
        logger.info(
            f"请求开始: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None
            }
        )

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录响应信息
        logger.info(
            f"请求完成: {request.method} {request.url.path} - "
            f"状态码: {response.status_code} - 耗时: {process_time:.3f}s",
            extra={
                "method": request.method,
                "url": str(request.url),
                "status_code": response.status_code,
                "process_time": process_time
            }
        )

        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)

        return response


# 在 main.py 中添加中间件
# app.add_middleware(LoggingMiddleware)
```

这个完整的 FastAPI 集成指南涵盖了项目结构、配置、认证、测试和监控等各个方面，为开发者提供了生产就绪的解决方案。

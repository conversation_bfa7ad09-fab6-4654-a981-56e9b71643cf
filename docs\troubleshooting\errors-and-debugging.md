# 错误处理和调试指南

本指南帮助您识别、理解和解决使用 SQLAlchemy CRUD Plus 时可能遇到的常见问题。

## 🚨 常见错误类型

### 1. ModelColumnError

**错误描述**：当尝试访问模型中不存在的字段时抛出。

```python
# ❌ 错误示例
users = await user_crud.select_models(
    session,
    non_existent_field="value"  # 字段不存在
)
```

**错误信息**：
```
ModelColumnError: Column 'non_existent_field' does not exist in model 'User'
```

**解决方案**：
```python
# ✅ 正确做法
# 1. 检查模型定义
class User(Base):
    __tablename__ = 'users'
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    email: Mapped[str] = mapped_column(String(100))

# 2. 使用正确的字段名
users = await user_crud.select_models(
    session,
    name="张三",  # 使用存在的字段
    email__like="%@example.com"
)

# 3. 动态检查字段是否存在
def safe_filter(**kwargs):
    valid_columns = {c.name for c in User.__table__.columns}
    filtered_kwargs = {}
    
    for key, value in kwargs.items():
        field_name = key.split('__')[0]  # 去除操作符
        if field_name in valid_columns:
            filtered_kwargs[key] = value
        else:
            print(f"警告: 字段 '{field_name}' 不存在，已忽略")
    
    return filtered_kwargs
```

### 2. SelectOperatorError

**错误描述**：使用了不支持的查询操作符。

```python
# ❌ 错误示例
users = await user_crud.select_models(
    session,
    age__invalid_operator=30  # 不支持的操作符
)
```

**错误信息**：
```
SelectOperatorError: Operator 'invalid_operator' is not supported
```

**支持的操作符列表**：
```python
SUPPORTED_OPERATORS = {
    # 比较操作符
    'gt', 'ge', 'lt', 'le', 'eq', 'ne',
    # 包含操作符
    'in', 'not_in', 'between',
    # 字符串操作符
    'like', 'not_like', 'ilike', 'not_ilike',
    'startswith', 'endswith', 'contains', 'match',
    # 身份比较
    'is', 'is_not', 'is_distinct_from', 'is_not_distinct_from',
    # 算术操作符
    'add', 'radd', 'sub', 'rsub', 'mul', 'rmul',
    'truediv', 'rtruediv', 'floordiv', 'rfloordiv', 'mod', 'rmod',
    # 字符串变换
    'concat'
}

# ✅ 正确使用
users = await user_crud.select_models(
    session,
    age__gt=30,           # 大于
    name__like='%张%',    # 模糊匹配
    email__endswith='@qq.com'  # 结尾匹配
)
```

### 3. MultipleResultsError

**错误描述**：期望单个结果但查询返回多个结果。

```python
# ❌ 可能出错的情况
user = await user_crud.select_model_by_column(
    session,
    name="张三"  # 可能有多个同名用户
)
```

**错误信息**：
```
MultipleResultsError: Multiple rows were found when exactly one was expected
```

**解决方案**：
```python
# ✅ 解决方案1：使用更具体的条件
user = await user_crud.select_model_by_column(
    session,
    email="<EMAIL>"  # 邮箱通常是唯一的
)

# ✅ 解决方案2：使用 select_models 获取所有结果
users = await user_crud.select_models(
    session,
    name="张三"
)
if len(users) == 1:
    user = users[0]
elif len(users) > 1:
    # 处理多个结果的情况
    print(f"找到 {len(users)} 个同名用户")
else:
    # 没有找到结果
    user = None

# ✅ 解决方案3：添加额外的过滤条件
user = await user_crud.select_model_by_column(
    session,
    name="张三",
    is_active=True,
    created_at__gte=datetime.now() - timedelta(days=30)
)
```

### 4. CompositePrimaryKeysError

**错误描述**：复合主键操作错误。

```python
# 复合主键模型
class UserRole(Base):
    __tablename__ = 'user_roles'
    user_id: Mapped[int] = mapped_column(primary_key=True)
    role_id: Mapped[int] = mapped_column(primary_key=True)

# ❌ 错误用法
user_role = await user_role_crud.select_model(session, pk=1)  # 应该传入元组
```

**解决方案**：
```python
# ✅ 正确用法
user_role = await user_role_crud.select_model(session, pk=(1, 2))  # 传入元组

# 创建复合主键记录
user_role_data = UserRoleCreate(user_id=1, role_id=2)
user_role = await user_role_crud.create_model(session, user_role_data)

# 更新复合主键记录
await user_role_crud.update_model(
    session,
    pk=(1, 2),
    obj={"assigned_at": datetime.now()}
)

# 删除复合主键记录
await user_role_crud.delete_model(session, pk=(1, 2))
```

### 5. LoadingStrategyError

**错误描述**：使用了不支持的加载策略。

```python
# ❌ 错误示例
user = await user_crud.select_model(
    session,
    pk=1,
    load_strategies={'posts': 'invalid_strategy'}
)
```

**支持的加载策略**：
```python
SUPPORTED_STRATEGIES = {
    'selectinload',    # 推荐用于一对多关系
    'joinedload',      # 推荐用于一对一关系
    'subqueryload',    # 用于复杂关系
    'contains_eager',  # 与 JOIN 一起使用
    'raiseload',       # 禁止懒加载
    'noload',          # 不加载关系
    'immediateload',   # 立即加载
    'lazyload',        # 懒加载（默认）
}

# ✅ 正确使用
user = await user_crud.select_model(
    session,
    pk=1,
    load_strategies={
        'posts': 'selectinload',
        'profile': 'joinedload'
    }
)
```

### 6. JoinConditionError

**错误描述**：JOIN 条件配置错误。

```python
# ❌ 错误示例
users = await user_crud.select_models(
    session,
    join_conditions={'invalid_relation': 'inner'}
)
```

**解决方案**：
```python
# ✅ 正确配置 JOIN 条件
users = await user_crud.select_models(
    session,
    join_conditions={
        'posts': 'inner',      # 关系名必须存在
        'profile': 'left'
    }
)

# 或使用列表形式（默认 inner join）
users = await user_crud.select_models(
    session,
    join_conditions=['posts', 'profile']
)

# 使用 JoinConfig 进行高级配置
from sqlalchemy_crud_plus.types import JoinConfig

users = await user_crud.select_models(
    session,
    join_conditions=[
        JoinConfig(
            model=Post,
            join_on=User.id == Post.author_id,
            join_type='left'
        )
    ]
)
```

## 🔍 调试技巧

### 1. 启用 SQL 日志

```python
# 开发环境配置
from sqlalchemy.ext.asyncio import create_async_engine

engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # 打印所有 SQL 语句
    echo_pool=True,  # 打印连接池信息
)

# 更详细的日志配置
import logging

# 设置 SQLAlchemy 日志级别
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
logging.getLogger('sqlalchemy.dialects').setLevel(logging.DEBUG)
logging.getLogger('sqlalchemy.pool').setLevel(logging.DEBUG)
logging.getLogger('sqlalchemy.orm').setLevel(logging.DEBUG)
```

### 2. 查询分析工具

```python
import time
from contextlib import asynccontextmanager
from sqlalchemy import text

@asynccontextmanager
async def analyze_query(session, description="查询"):
    """查询分析上下文管理器"""
    start_time = time.time()
    
    # 获取查询前的统计信息
    before_stats = await session.execute(text("SHOW STATUS LIKE 'Questions'"))
    
    try:
        yield
    finally:
        # 获取查询后的统计信息
        after_stats = await session.execute(text("SHOW STATUS LIKE 'Questions'"))
        duration = time.time() - start_time
        
        print(f"{description} 执行时间: {duration:.3f}s")

# 使用示例
async def debug_user_query(session):
    async with analyze_query(session, "用户查询"):
        users = await user_crud.select_models(
            session,
            load_strategies=['posts'],
            limit=100
        )
    return users
```

### 3. 内存使用监控

```python
import tracemalloc
import psutil
import os

def monitor_memory():
    """监控内存使用"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss_mb': memory_info.rss / 1024 / 1024,
        'vms_mb': memory_info.vms / 1024 / 1024,
        'percent': process.memory_percent()
    }

async def debug_memory_usage():
    """调试内存使用情况"""
    # 启动内存跟踪
    tracemalloc.start()
    
    before_memory = monitor_memory()
    
    # 执行可能消耗大量内存的操作
    async with db_manager.get_session() as session:
        users = await user_crud.select_models(
            session,
            load_strategies=['posts', 'profile'],
            limit=1000
        )
    
    after_memory = monitor_memory()
    
    # 获取内存快照
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')
    
    print(f"内存使用变化: {after_memory['rss_mb'] - before_memory['rss_mb']:.2f} MB")
    print("内存使用最多的前 10 行代码:")
    for stat in top_stats[:10]:
        print(stat)
    
    tracemalloc.stop()
```

### 4. 查询性能分析

```python
from sqlalchemy import text

async def explain_query(session, query_sql):
    """分析查询执行计划"""
    explain_sql = f"EXPLAIN ANALYZE {query_sql}"
    result = await session.execute(text(explain_sql))
    
    print("查询执行计划:")
    for row in result:
        print(row[0])

# 使用示例
async def analyze_user_query(session):
    # 构建查询
    stmt = user_crud.crud._build_select_statement(
        load_strategies=['posts'],
        name__like='%张%'
    )
    
    # 分析执行计划
    await explain_query(session, str(stmt.compile(compile_kwargs={"literal_binds": True})))
    
    # 执行实际查询
    users = await user_crud.select_models(
        session,
        load_strategies=['posts'],
        name__like='%张%'
    )
    
    return users
```

## 🛠️ 常见问题解决

### 1. N+1 查询问题

**问题**：访问关系属性时触发大量额外查询。

```python
# ❌ 问题代码
users = await user_crud.select_models(session, limit=10)
for user in users:
    print(f"用户 {user.name} 有 {len(user.posts)} 篇文章")  # 每次都会查询数据库
```

**解决方案**：
```python
# ✅ 使用预加载
users = await user_crud.select_models(
    session,
    load_strategies=['posts'],  # 预加载文章
    limit=10
)
for user in users:
    print(f"用户 {user.name} 有 {len(user.posts)} 篇文章")  # 不会触发额外查询
```

### 2. 事务死锁

**问题**：并发事务导致死锁。

**解决方案**：
```python
import asyncio
from sqlalchemy.exc import OperationalError

async def safe_transaction_operation(session, operation_func, max_retries=3):
    """安全的事务操作，支持重试"""
    for attempt in range(max_retries):
        try:
            async with session.begin():
                result = await operation_func(session)
                return result
        except OperationalError as e:
            if "deadlock" in str(e).lower() and attempt < max_retries - 1:
                # 等待随机时间后重试
                await asyncio.sleep(0.1 * (2 ** attempt))
                continue
            raise
    
    raise Exception(f"操作在 {max_retries} 次重试后仍然失败")

# 使用示例
async def update_user_safely(user_id: int, update_data: dict):
    async def operation(session):
        return await user_crud.update_model(session, pk=user_id, obj=update_data)
    
    async with db_manager.get_session() as session:
        return await safe_transaction_operation(session, operation)
```

### 3. 连接池耗尽

**问题**：数据库连接池耗尽导致应用阻塞。

**解决方案**：
```python
# 1. 优化连接池配置
engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,           # 增加连接池大小
    max_overflow=30,        # 增加溢出连接数
    pool_timeout=30,        # 设置获取连接超时时间
    pool_recycle=3600,      # 连接回收时间
    pool_pre_ping=True,     # 连接预检查
)

# 2. 监控连接池状态
async def monitor_connection_pool():
    pool = engine.pool
    print(f"连接池大小: {pool.size()}")
    print(f"已检出连接: {pool.checkedout()}")
    print(f"溢出连接: {pool.overflow()}")
    print(f"无效连接: {pool.invalidated()}")

# 3. 确保正确关闭会话
async def safe_database_operation():
    async with db_manager.get_session() as session:
        try:
            # 数据库操作
            users = await user_crud.select_models(session)
            return users
        except Exception as e:
            await session.rollback()
            raise
        # session 会自动关闭
```

通过遵循这些调试和错误处理指南，您可以更有效地识别和解决问题，确保应用的稳定运行。

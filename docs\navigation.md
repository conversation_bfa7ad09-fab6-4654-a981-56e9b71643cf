# 文档导航

本页面提供 SQLAlchemy CRUD Plus 完整文档的导航指南，帮助您快速找到所需信息。

## 📚 文档结构概览

```
docs/
├── 🏠 首页
│   ├── index.md                    # 项目介绍和特性概览
│   └── navigation.md               # 文档导航（本页面）
│
├── 📦 安装和配置
│   └── installing.md               # 详细安装指南
│
├── 🚀 快速开始
│   └── getting-started/
│       └── quick-start.md          # 5分钟上手指南
│
├── 📖 基础用法
│   └── usage/
│       └── crud.md                 # CRUD 操作详解
│
├── 🔗 关系查询
│   └── relationships/
│       └── overview.md             # 关系查询完整指南
│
├── 🎯 高级功能
│   └── advanced/
│       ├── filter.md               # 34+ 种过滤操作符
│       └── transaction.md          # 事务控制和管理
│
├── 🏆 最佳实践
│   └── best-practices/
│       ├── overview.md             # 生产环境指南
│       └── performance.md          # 性能优化策略
│
├── 🔌 框架集成
│   └── integrations/
│       ├── fastapi.md              # FastAPI 完整集成
│       └── django-style.md         # Django 开发者迁移指南
│
├── 💡 实际应用
│   └── examples/
│       └── real-world-examples.md  # 电商系统等实际案例
│
├── 🛠️ 故障排除
│   └── troubleshooting/
│       └── errors-and-debugging.md # 错误处理和调试
│
├── 📋 API 参考
│   └── api/
│       ├── crud-plus.md            # CRUDPlus 类完整参考
│       └── errors.md               # 异常类型参考
│
└── 📝 项目信息
    ├── changelog.md                # 英文更新日志
    └── changelog-zh.md             # 中文更新日志
```

## 🎯 学习路径推荐

### 🌟 新手入门路径（30分钟）

1. **了解项目** → [项目介绍](index.md)
   - 了解核心特性和优势
   - 查看适用场景

2. **环境准备** → [安装指南](installing.md)
   - 安装依赖和配置环境
   - 验证安装是否成功

3. **快速上手** → [快速开始](getting-started/quick-start.md)
   - 5分钟完整示例
   - 掌握基础 CRUD 操作

4. **深入学习** → [基础用法](usage/crud.md)
   - 详细了解每个方法
   - 学习参数和选项

### 🚀 进阶开发路径（1-2小时）

1. **关系查询** → [关系查询指南](relationships/overview.md)
   - 预加载策略选择
   - JOIN 查询技巧
   - 避免 N+1 问题

2. **高级功能** → [过滤操作](advanced/filter.md) + [事务控制](advanced/transaction.md)
   - 34+ 种过滤操作符
   - 复杂查询条件
   - 事务管理最佳实践

3. **性能优化** → [性能优化指南](best-practices/performance.md)
   - 查询优化技巧
   - 批量操作策略
   - 监控和分析

### 🏢 生产环境路径（2-4小时）

1. **最佳实践** → [生产环境指南](best-practices/overview.md)
   - 项目结构设计
   - 错误处理策略
   - 配置管理

2. **框架集成** → [FastAPI 集成](integrations/fastapi.md)
   - 完整项目示例
   - 认证和授权
   - API 设计模式

3. **实际应用** → [真实案例](examples/real-world-examples.md)
   - 电商系统实现
   - 复杂业务逻辑
   - 数据库设计

4. **故障排除** → [错误处理指南](troubleshooting/errors-and-debugging.md)
   - 常见问题解决
   - 调试技巧
   - 性能分析

## 📖 按功能分类导航

### 🔧 核心功能

| 功能 | 文档 | 描述 |
|------|------|------|
| **基础 CRUD** | [CRUD 操作](usage/crud.md) | 创建、查询、更新、删除操作 |
| **关系查询** | [关系查询](relationships/overview.md) | 预加载、JOIN、嵌套关系 |
| **过滤查询** | [过滤操作](advanced/filter.md) | 34+ 种过滤操作符 |
| **事务控制** | [事务管理](advanced/transaction.md) | 事务、保存点、并发控制 |

### 🎯 高级特性

| 特性 | 文档 | 描述 |
|------|------|------|
| **批量操作** | [性能优化](best-practices/performance.md) | 高效的批量创建、更新、删除 |
| **复合主键** | [API 参考](api/crud-plus.md) | 多字段主键支持 |
| **OR 条件** | [过滤操作](advanced/filter.md) | 复杂条件组合查询 |
| **类型安全** | [快速开始](getting-started/quick-start.md) | 完整的类型提示支持 |

### 🔌 集成和扩展

| 集成 | 文档 | 描述 |
|------|------|------|
| **FastAPI** | [FastAPI 集成](integrations/fastapi.md) | Web API 开发完整方案 |
| **Django 风格** | [Django 迁移](integrations/django-style.md) | Django 开发者迁移指南 |
| **Pydantic** | [快速开始](getting-started/quick-start.md) | 数据验证和序列化 |

## 🎨 按使用场景导航

### 💼 业务场景

| 场景 | 推荐文档 | 关键特性 |
|------|----------|----------|
| **Web API 开发** | [FastAPI 集成](integrations/fastapi.md) | 认证、分页、错误处理 |
| **数据分析** | [关系查询](relationships/overview.md) + [性能优化](best-practices/performance.md) | 复杂查询、聚合操作 |
| **微服务** | [最佳实践](best-practices/overview.md) | 事务控制、错误处理 |
| **原型开发** | [快速开始](getting-started/quick-start.md) | 快速上手、简单配置 |

### 🔍 问题解决

| 问题类型 | 解决方案 | 相关文档 |
|----------|----------|----------|
| **性能问题** | 查询优化、预加载策略 | [性能优化](best-practices/performance.md) |
| **错误调试** | 异常处理、日志分析 | [故障排除](troubleshooting/errors-and-debugging.md) |
| **功能实现** | API 参考、示例代码 | [API 参考](api/crud-plus.md) |
| **迁移升级** | 版本变更、兼容性 | [更新日志](changelog-zh.md) |

## 🔍 快速查找

### 📋 常用操作速查

| 操作 | 快速链接 | 示例代码位置 |
|------|----------|-------------|
| **创建记录** | [CRUD → 创建](usage/crud.md#创建操作) | [快速开始](getting-started/quick-start.md) |
| **查询记录** | [CRUD → 查询](usage/crud.md#查询操作) | [快速开始](getting-started/quick-start.md) |
| **更新记录** | [CRUD → 更新](usage/crud.md#更新操作) | [快速开始](getting-started/quick-start.md) |
| **删除记录** | [CRUD → 删除](usage/crud.md#删除操作) | [快速开始](getting-started/quick-start.md) |
| **关系查询** | [关系查询](relationships/overview.md) | [实际案例](examples/real-world-examples.md) |
| **过滤条件** | [过滤操作](advanced/filter.md) | [Django 风格](integrations/django-style.md) |

### 🛠️ API 速查

| API 方法 | 文档链接 | 用途 |
|----------|----------|------|
| `create_model()` | [API 参考](api/crud-plus.md#create_model) | 创建单个记录 |
| `create_models()` | [API 参考](api/crud-plus.md#create_models) | 批量创建记录 |
| `select_model()` | [API 参考](api/crud-plus.md#select_model) | 查询单个记录 |
| `select_models()` | [API 参考](api/crud-plus.md#select_models) | 查询多个记录 |
| `update_model()` | [API 参考](api/crud-plus.md#update_model) | 更新记录 |
| `delete_model()` | [API 参考](api/crud-plus.md#delete_model) | 删除记录 |
| `count()` | [API 参考](api/crud-plus.md#count) | 统计记录数 |
| `exists()` | [API 参考](api/crud-plus.md#exists) | 检查存在性 |

## 🎓 学习建议

### 📚 阅读顺序建议

**初学者**：
1. 项目介绍 → 安装指南 → 快速开始 → 基础用法

**有经验开发者**：
1. 项目介绍 → 快速开始 → 关系查询 → 高级功能 → 最佳实践

**Django 开发者**：
1. 项目介绍 → Django 迁移指南 → 快速开始 → 高级功能

**生产环境部署**：
1. 最佳实践 → 性能优化 → FastAPI 集成 → 故障排除

### 💡 学习技巧

1. **动手实践**：每个章节都有可运行的示例代码
2. **循序渐进**：从简单示例开始，逐步学习复杂功能
3. **参考案例**：查看实际应用示例了解最佳实践
4. **社区交流**：遇到问题可以在 Discord 社区求助

## 🔗 外部资源

### 📖 相关文档

- [SQLAlchemy 2.0 官方文档](https://docs.sqlalchemy.org/en/20/)
- [Pydantic 官方文档](https://docs.pydantic.dev/)
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)

### 🌐 社区资源

- [GitHub 仓库](https://github.com/fastapi-practices/sqlalchemy-crud-plus)
- [Discord 社区](https://wu-clan.github.io/homepage/)
- [问题反馈](https://github.com/fastapi-practices/sqlalchemy-crud-plus/issues)

### 📺 视频教程

- 即将推出视频教程系列
- 关注项目获取最新更新

---

## 📞 需要帮助？

如果您在使用过程中遇到问题：

1. **查看文档**：首先查看相关章节的详细说明
2. **搜索问题**：在 [GitHub Issues](https://github.com/fastapi-practices/sqlalchemy-crud-plus/issues) 中搜索类似问题
3. **提交问题**：如果没有找到解决方案，请提交新的 Issue
4. **社区讨论**：加入 Discord 社区与其他开发者交流

祝您学习愉快！🎉

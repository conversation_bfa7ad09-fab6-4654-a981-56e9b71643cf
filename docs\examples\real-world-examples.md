# 实际应用场景示例

本文档提供了 SQLAlchemy CRUD Plus 在真实项目中的应用示例，涵盖常见的业务场景和解决方案。

## 🏪 电商系统示例

### 数据模型设计

```python
# models/ecommerce.py
from datetime import datetime
from decimal import Decimal
from enum import Enum
from sqlalchemy import String, Text, Numeric, DateTime, ForeignKey, Boolean, func
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database import Base


class OrderStatus(str, Enum):
    PENDING = "pending"
    PAID = "paid"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class User(Base):
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    full_name: Mapped[str] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    
    # 关系
    orders: Mapped[list["Order"]] = relationship(back_populates="customer")
    cart_items: Mapped[list["CartItem"]] = relationship(back_populates="user")


class Category(Base):
    __tablename__ = "categories"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(default=True)
    
    # 关系
    products: Mapped[list["Product"]] = relationship(back_populates="category")


class Product(Base):
    __tablename__ = "products"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(200), index=True)
    description: Mapped[str] = mapped_column(Text)
    price: Mapped[Decimal] = mapped_column(Numeric(10, 2))
    stock_quantity: Mapped[int] = mapped_column(default=0)
    is_active: Mapped[bool] = mapped_column(default=True, index=True)
    category_id: Mapped[int] = mapped_column(ForeignKey("categories.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    
    # 关系
    category: Mapped[Category] = relationship(back_populates="products")
    order_items: Mapped[list["OrderItem"]] = relationship(back_populates="product")
    cart_items: Mapped[list["CartItem"]] = relationship(back_populates="product")


class Order(Base):
    __tablename__ = "orders"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    order_number: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    customer_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    status: Mapped[OrderStatus] = mapped_column(default=OrderStatus.PENDING, index=True)
    total_amount: Mapped[Decimal] = mapped_column(Numeric(10, 2))
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # 关系
    customer: Mapped[User] = relationship(back_populates="orders")
    items: Mapped[list["OrderItem"]] = relationship(back_populates="order")


class OrderItem(Base):
    __tablename__ = "order_items"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    order_id: Mapped[int] = mapped_column(ForeignKey("orders.id"))
    product_id: Mapped[int] = mapped_column(ForeignKey("products.id"))
    quantity: Mapped[int] = mapped_column()
    unit_price: Mapped[Decimal] = mapped_column(Numeric(10, 2))
    
    # 关系
    order: Mapped[Order] = relationship(back_populates="items")
    product: Mapped[Product] = relationship(back_populates="order_items")


class CartItem(Base):
    __tablename__ = "cart_items"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    product_id: Mapped[int] = mapped_column(ForeignKey("products.id"))
    quantity: Mapped[int] = mapped_column()
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    
    # 关系
    user: Mapped[User] = relationship(back_populates="cart_items")
    product: Mapped[Product] = relationship(back_populates="cart_items")
```

### 业务逻辑实现

```python
# services/ecommerce_service.py
from decimal import Decimal
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_crud_plus import CRUDPlus

from models.ecommerce import User, Product, Order, OrderItem, CartItem, OrderStatus
from schemas.ecommerce import OrderCreate, CartItemCreate


class EcommerceService:
    def __init__(self):
        self.user_crud = CRUDPlus(User)
        self.product_crud = CRUDPlus(Product)
        self.order_crud = CRUDPlus(Order)
        self.order_item_crud = CRUDPlus(OrderItem)
        self.cart_crud = CRUDPlus(CartItem)
    
    async def get_product_catalog(
        self,
        session: AsyncSession,
        category_id: Optional[int] = None,
        min_price: Optional[Decimal] = None,
        max_price: Optional[Decimal] = None,
        search: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> dict:
        """获取商品目录"""
        filters = {"is_active": True}
        
        if category_id:
            filters["category_id"] = category_id
        
        if min_price is not None:
            filters["price__gte"] = min_price
        
        if max_price is not None:
            filters["price__lte"] = max_price
        
        if search:
            filters["__or__"] = {
                "name__ilike": f"%{search}%",
                "description__ilike": f"%{search}%"
            }
        
        offset = (page - 1) * page_size
        
        # 获取商品列表（预加载分类信息）
        products = await self.product_crud.select_models(
            session,
            load_strategies=["category"],
            offset=offset,
            limit=page_size,
            **filters
        )
        
        # 获取总数
        total = await self.product_crud.count(session, **filters)
        
        return {
            "products": products,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def add_to_cart(
        self,
        session: AsyncSession,
        user_id: int,
        product_id: int,
        quantity: int
    ) -> CartItem:
        """添加商品到购物车"""
        # 检查商品是否存在且有库存
        product = await self.product_crud.select_model(session, pk=product_id)
        if not product or not product.is_active:
            raise ValueError("商品不存在或已下架")
        
        if product.stock_quantity < quantity:
            raise ValueError(f"库存不足，当前库存：{product.stock_quantity}")
        
        # 检查购物车中是否已有该商品
        existing_item = await self.cart_crud.select_model_by_column(
            session,
            user_id=user_id,
            product_id=product_id
        )
        
        if existing_item:
            # 更新数量
            new_quantity = existing_item.quantity + quantity
            if product.stock_quantity < new_quantity:
                raise ValueError(f"库存不足，当前库存：{product.stock_quantity}")
            
            await self.cart_crud.update_model(
                session,
                pk=existing_item.id,
                obj={"quantity": new_quantity}
            )
            return await self.cart_crud.select_model(session, pk=existing_item.id)
        else:
            # 创建新的购物车项
            cart_item_data = CartItemCreate(
                user_id=user_id,
                product_id=product_id,
                quantity=quantity
            )
            return await self.cart_crud.create_model(session, cart_item_data)
    
    async def get_user_cart(
        self,
        session: AsyncSession,
        user_id: int
    ) -> List[CartItem]:
        """获取用户购物车"""
        return await self.cart_crud.select_models(
            session,
            load_strategies=["product", "product.category"],
            user_id=user_id
        )
    
    async def create_order_from_cart(
        self,
        session: AsyncSession,
        user_id: int
    ) -> Order:
        """从购物车创建订单"""
        async with session.begin():
            # 获取购物车商品
            cart_items = await self.get_user_cart(session, user_id)
            if not cart_items:
                raise ValueError("购物车为空")
            
            # 检查库存并计算总金额
            total_amount = Decimal('0')
            order_items_data = []
            
            for cart_item in cart_items:
                product = cart_item.product
                if not product.is_active:
                    raise ValueError(f"商品 {product.name} 已下架")
                
                if product.stock_quantity < cart_item.quantity:
                    raise ValueError(f"商品 {product.name} 库存不足")
                
                item_total = product.price * cart_item.quantity
                total_amount += item_total
                
                order_items_data.append({
                    "product_id": product.id,
                    "quantity": cart_item.quantity,
                    "unit_price": product.price
                })
            
            # 生成订单号
            import uuid
            order_number = f"ORD-{uuid.uuid4().hex[:8].upper()}"
            
            # 创建订单
            order_data = OrderCreate(
                order_number=order_number,
                customer_id=user_id,
                total_amount=total_amount,
                status=OrderStatus.PENDING
            )
            order = await self.order_crud.create_model(session, order_data, flush=True)
            
            # 创建订单项并更新库存
            for item_data in order_items_data:
                item_data["order_id"] = order.id
                await self.order_item_crud.create_model(session, item_data)
                
                # 减少库存
                await self.product_crud.update_model(
                    session,
                    pk=item_data["product_id"],
                    obj={"stock_quantity": Product.stock_quantity - item_data["quantity"]}
                )
            
            # 清空购物车
            await self.cart_crud.delete_model_by_column(session, user_id=user_id)
            
            return order
    
    async def get_user_orders(
        self,
        session: AsyncSession,
        user_id: int,
        status: Optional[OrderStatus] = None,
        page: int = 1,
        page_size: int = 10
    ) -> dict:
        """获取用户订单列表"""
        filters = {"customer_id": user_id}
        if status:
            filters["status"] = status
        
        offset = (page - 1) * page_size
        
        orders = await self.order_crud.select_models_order(
            session,
            sort_columns="created_at",
            sort_orders="desc",
            load_strategies=["items", "items.product"],
            offset=offset,
            limit=page_size,
            **filters
        )
        
        total = await self.order_crud.count(session, **filters)
        
        return {
            "orders": orders,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def update_order_status(
        self,
        session: AsyncSession,
        order_id: int,
        new_status: OrderStatus
    ) -> Order:
        """更新订单状态"""
        order = await self.order_crud.select_model(session, pk=order_id)
        if not order:
            raise ValueError("订单不存在")
        
        # 状态转换验证
        valid_transitions = {
            OrderStatus.PENDING: [OrderStatus.PAID, OrderStatus.CANCELLED],
            OrderStatus.PAID: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
            OrderStatus.SHIPPED: [OrderStatus.DELIVERED],
            OrderStatus.DELIVERED: [],
            OrderStatus.CANCELLED: []
        }
        
        if new_status not in valid_transitions.get(order.status, []):
            raise ValueError(f"无法从 {order.status} 转换到 {new_status}")
        
        # 如果取消订单，需要恢复库存
        if new_status == OrderStatus.CANCELLED and order.status != OrderStatus.CANCELLED:
            order_items = await self.order_item_crud.select_models(
                session,
                load_strategies=["product"],
                order_id=order_id
            )
            
            for item in order_items:
                await self.product_crud.update_model(
                    session,
                    pk=item.product_id,
                    obj={"stock_quantity": Product.stock_quantity + item.quantity}
                )
        
        # 更新订单状态
        await self.order_crud.update_model(
            session,
            pk=order_id,
            obj={"status": new_status}
        )
        
        return await self.order_crud.select_model(session, pk=order_id)
    
    async def get_sales_report(
        self,
        session: AsyncSession,
        start_date: datetime,
        end_date: datetime
    ) -> dict:
        """获取销售报告"""
        # 获取指定时间段的已完成订单
        orders = await self.order_crud.select_models(
            session,
            load_strategies=["items", "items.product", "items.product.category"],
            status=OrderStatus.DELIVERED,
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # 统计数据
        total_orders = len(orders)
        total_revenue = sum(order.total_amount for order in orders)
        
        # 按分类统计
        category_stats = {}
        product_stats = {}
        
        for order in orders:
            for item in order.items:
                product = item.product
                category = product.category
                
                # 分类统计
                if category.name not in category_stats:
                    category_stats[category.name] = {
                        "quantity": 0,
                        "revenue": Decimal('0')
                    }
                category_stats[category.name]["quantity"] += item.quantity
                category_stats[category.name]["revenue"] += item.unit_price * item.quantity
                
                # 商品统计
                if product.name not in product_stats:
                    product_stats[product.name] = {
                        "quantity": 0,
                        "revenue": Decimal('0')
                    }
                product_stats[product.name]["quantity"] += item.quantity
                product_stats[product.name]["revenue"] += item.unit_price * item.quantity
        
        return {
            "period": {
                "start_date": start_date,
                "end_date": end_date
            },
            "summary": {
                "total_orders": total_orders,
                "total_revenue": total_revenue,
                "average_order_value": total_revenue / total_orders if total_orders > 0 else 0
            },
            "category_stats": category_stats,
            "top_products": sorted(
                product_stats.items(),
                key=lambda x: x[1]["revenue"],
                reverse=True
            )[:10]
        }


# 使用示例
ecommerce_service = EcommerceService()
```

### API 路由实现

```python
# api/v1/ecommerce.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from decimal import Decimal
from typing import Optional

from api.deps import get_db, get_current_user
from services.ecommerce_service import ecommerce_service
from models.ecommerce import User, OrderStatus
from schemas.ecommerce import CartItemAdd, OrderResponse

router = APIRouter()


@router.get("/products")
async def get_products(
    category_id: Optional[int] = Query(None),
    min_price: Optional[Decimal] = Query(None),
    max_price: Optional[Decimal] = Query(None),
    search: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    session: AsyncSession = Depends(get_db)
):
    """获取商品列表"""
    return await ecommerce_service.get_product_catalog(
        session, category_id, min_price, max_price, search, page, page_size
    )


@router.post("/cart/add")
async def add_to_cart(
    item: CartItemAdd,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """添加商品到购物车"""
    try:
        cart_item = await ecommerce_service.add_to_cart(
            session, current_user.id, item.product_id, item.quantity
        )
        return cart_item
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/cart")
async def get_cart(
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取购物车"""
    return await ecommerce_service.get_user_cart(session, current_user.id)


@router.post("/orders", response_model=OrderResponse)
async def create_order(
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建订单"""
    try:
        order = await ecommerce_service.create_order_from_cart(session, current_user.id)
        return order
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/orders")
async def get_orders(
    status: Optional[OrderStatus] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=50),
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取订单列表"""
    return await ecommerce_service.get_user_orders(
        session, current_user.id, status, page, page_size
    )


@router.get("/reports/sales")
async def get_sales_report(
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取销售报告（管理员功能）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="权限不足")
    
    return await ecommerce_service.get_sales_report(session, start_date, end_date)
```

这个电商系统示例展示了如何使用 SQLAlchemy CRUD Plus 构建复杂的业务逻辑，包括购物车管理、订单处理、库存控制和销售报告等功能。

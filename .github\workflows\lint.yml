name: ci

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest
    name: lint ${{ matrix.python-version }}
    strategy:
      matrix:
        python-version: [ '3.10', '3.11', '3.12' ]
      fail-fast: false
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3

      - name: Set up Python ${{ matrix.python-version }}
        run: uv python install ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          uv sync --group lint

      - name: Run lint
        run: |
          source .venv/bin/activate
          chmod 755 pre-commit.sh
          ./pre-commit.sh

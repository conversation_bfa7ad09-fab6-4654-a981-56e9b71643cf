<a id="v1.10.0"></a>
# [v1.10.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/v1.10.0) - 2025-06-16

## What's Changed
* Update changelog for 1.9.0 by [@wu-clan](https://github.com/wu-clan) in [#46](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/46)
* Simplify or related operations by [@wu-clan](https://github.com/wu-clan) in [#48](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/48)
* Optimize code structure and test cases by [@wu-clan](https://github.com/wu-clan) in [#49](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/49)
* Add support for relationship select by [@wu-clan](https://github.com/wu-clan) in [#50](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/50)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.9.0...v1.10.0

[Changes][v1.10.0]


<a id="1.9.0"></a>
# [1.9.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.9.0) - 2025-05-05

## What's Changed
* Update changelog for 1.8.0 by [@wu-clan](https://github.com/wu-clan) in [#42](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/42)
* Add composite primary key support by [@wu-clan](https://github.com/wu-clan) in [#44](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/44)
* Prepare for 1.9.0 release by [@wu-clan](https://github.com/wu-clan) in [#45](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/45)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.8.0...1.9.0

[Changes][1.9.0]


<a id="1.8.0"></a>
# [1.8.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.8.0) - 2025-04-27

## What's Changed
* Update changelog for 1.7.0 by [@wu-clan](https://github.com/wu-clan) in [#39](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/39)
* Add where clause support to select by [@wu-clan](https://github.com/wu-clan) in [#40](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/40)
* Prepare for 1.8.0 release by [@wu-clan](https://github.com/wu-clan) in [#41](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/41)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.7.0...1.8.0

[Changes][1.8.0]


<a id="1.7.0"></a>
# [1.7.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.7.0) - 2025-04-13

## What's Changed
* Update website and repository url by [@wu-clan](https://github.com/wu-clan) in [#34](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/34)
* Add count and exists usages by [@wu-clan](https://github.com/wu-clan) in [#37](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/37)
* Prepare for 1.7.0 release by [@wu-clan](https://github.com/wu-clan) in [#38](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/38)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.6.0...1.7.0

[Changes][1.7.0]


<a id="1.6.0"></a>
# [1.6.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.6.0) - 2024-11-09

## What's Changed
* Add the flush usage by [@wu-clan](https://github.com/wu-clan) in [#32](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/32)
* Prepare for 1.6.0 release by [@wu-clan](https://github.com/wu-clan) in [#33](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/33)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.5.0...1.6.0

[Changes][1.6.0]


<a id="1.5.0"></a>
# [1.5.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.5.0) - 2024-11-03

## What's Changed
* Update some usage documents by [@wu-clan](https://github.com/wu-clan) in [#27](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/27)
* Update the interactive link address by [@wu-clan](https://github.com/wu-clan) in [#28](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/28)
* update interactive link descriptions by [@wu-clan](https://github.com/wu-clan) in [#29](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/29)
* Update package manager pdm to uv by [@wu-clan](https://github.com/wu-clan) in [#31](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/31)
* Add create and update kwargs usage by [@wu-clan](https://github.com/wu-clan) in [#30](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/30)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.4.0...1.5.0

[Changes][1.5.0]


<a id="1.4.0"></a>
# [v1.4.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.4.0) - 2024-08-27

## What's Changed
* Add lint and test ci by [@wu-clan](https://github.com/wu-clan) in [#24](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/24)
* Fix the cache for docs ci by [@wu-clan](https://github.com/wu-clan) in [#25](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/25)
* Update model primary key for dynamic retrieval by [@DavidSche](https://github.com/DavidSche) in [#23](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/23)
* Prepare for 1.4.0 release by [@wu-clan](https://github.com/wu-clan) in [#26](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/26)

## New Contributors
* [@DavidSche](https://github.com/DavidSche) made their first contribution in [#23](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/23)

**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.3.0...1.4.0

[Changes][1.4.0]


<a id="1.3.0"></a>
# [v1.3.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.3.0) - 2024-08-25

## What's Changed
* Add mor and \_\_gor\_\_ filters  by [@wu-clan](https://github.com/wu-clan) in [#21](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/21)
* Prepare for 1.3.0 release by [@wu-clan](https://github.com/wu-clan) in [#22](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/22)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.2.0...1.3.0

[Changes][1.3.0]


<a id="1.2.0"></a>
# [v1.2.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.2.0) - 2024-08-24

## What's Changed
* Add select and sort constructors by [@wu-clan](https://github.com/wu-clan) in [#19](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/19)
* Prepare for 1.2.0 release by [@wu-clan](https://github.com/wu-clan) in [#20](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/20)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.1.0...1.2.0

[Changes][1.2.0]


<a id="1.1.0"></a>
# [v1.1.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.1.0) - 2024-08-24

## What's Changed
* Add mkdocs usage documentation by [@wu-clan](https://github.com/wu-clan) in [#12](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/12)
* Update some async functions definition by [@wu-clan](https://github.com/wu-clan) in [#13](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/13)
* Update ci for build docs by [@wu-clan](https://github.com/wu-clan) in [#14](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/14)
* Fix ci install pngquant user by [@wu-clan](https://github.com/wu-clan) in [#15](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/15)
* Fix docs ci run pip by [@wu-clan](https://github.com/wu-clan) in [#16](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/16)
* Prepare for 1.1.0 release by [@wu-clan](https://github.com/wu-clan) in [#17](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/17)
* Add ci for change logs by [@wu-clan](https://github.com/wu-clan) in [#18](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/18)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.0.0...1.1.0

[Changes][1.1.0]


<a id="1.0.0"></a>
# [v1.0.0](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/1.0.0) - 2024-08-12

## Major Event
CRUD methods have been refactored, check out the documentation for new usage!

## What's Changed
* Refactor code to enhance selection and operations by [@wu-clan](https://github.com/wu-clan) in [#9](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/9)
* Prepare for 1.0.0 release by [@wu-clan](https://github.com/wu-clan) in [#11](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/11)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/0.0.4...1.0.0

[Changes][1.0.0]


<a id="0.0.4"></a>
# [v0.0.4](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases/tag/0.0.4) - 2024-08-09

## What's Changed
* Add pep561 support to the library by [@wu-clan](https://github.com/wu-clan) in [#7](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/7)
* Add commit option to CRUD operations by [@wu-clan](https://github.com/wu-clan) in [#8](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/8)
* Prepare for 0.0.4 release by [@wu-clan](https://github.com/wu-clan) in [#10](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/10)


**Full Changelog**: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/0.0.3...0.0.4

[Changes][0.0.4]


[v1.10.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.9.0...v1.10.0
[1.9.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.8.0...1.9.0
[1.8.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.7.0...1.8.0
[1.7.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.6.0...1.7.0
[1.6.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.5.0...1.6.0
[1.5.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.4.0...1.5.0
[1.4.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.3.0...1.4.0
[1.3.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.2.0...1.3.0
[1.2.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.1.0...1.2.0
[1.1.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/1.0.0...1.1.0
[1.0.0]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/compare/0.0.4...1.0.0
[0.0.4]: https://github.com/fastapi-practices/sqlalchemy-crud-plus/tree/0.0.4

<!-- Generated by https://github.com/rhysd/changelog-from-release v3.9.0 -->

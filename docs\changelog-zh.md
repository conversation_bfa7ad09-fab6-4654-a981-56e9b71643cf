# 更新日志 (中文版)

记录 SQLAlchemy CRUD Plus 的版本更新和重要变化。

> 📝 **说明**: 这是中文版更新日志，英文版本请查看 [changelog.md](changelog.md)

## [v1.10.0] - 2025-06-16 🎉

### 🎯 主要更新

**关系查询增强**
- ✨ **新增关系查询支持** - 支持复杂的关系数据预加载和 JOIN 操作
- ✨ **优化查询性能** - 智能避免 N+1 查询问题，提升查询效率
- ✨ **增强 OR 条件操作** - 简化复杂条件查询的语法

### 🔧 技术改进

**代码结构优化**
- 📈 **重构代码结构** - 提高代码可维护性和扩展性
- 📈 **完善测试用例** - 增加测试覆盖率，确保代码质量
- 📈 **性能优化** - 优化内部查询构建逻辑

### 📚 相关 PR

- [#46](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/46) - 更新 1.9.0 版本更新日志
- [#48](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/48) - 简化 OR 相关操作
- [#49](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/49) - 优化代码结构和测试用例
- [#50](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/50) - 添加关系查询支持

### 🚀 使用示例

```python
# 新的关系查询功能
users = await user_crud.select_models(
    session,
    load_strategies={
        'posts': 'selectinload',
        'profile': 'joinedload'
    },
    join_conditions=['posts'],
    name__like='%张%'
)

# 简化的 OR 条件查询
users = await user_crud.select_models(
    session,
    __or__={
        'name__like': '%张%',
        'email__endswith': '@gmail.com'
    }
)
```

---

## [1.9.0] - 2025-05-05 🔑

### 🎯 主要更新

**复合主键支持**
- ✨ **新增复合主键支持** - 完整支持多字段主键的 CRUD 操作
- ✨ **增强主键处理** - 自动识别和处理不同类型的主键结构

### 📚 相关 PR

- [#42](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/42) - 更新 1.8.0 版本更新日志
- [#44](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/44) - 添加复合主键支持
- [#45](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/45) - 准备 1.9.0 版本发布

### 🚀 使用示例

```python
# 复合主键模型
class UserRole(Base):
    __tablename__ = 'user_roles'
    user_id: Mapped[int] = mapped_column(primary_key=True)
    role_id: Mapped[int] = mapped_column(primary_key=True)

# 复合主键操作
user_role = await user_role_crud.select_model(session, pk=(1, 2))
await user_role_crud.update_model(session, pk=(1, 2), obj={"status": "active"})
await user_role_crud.delete_model(session, pk=(1, 2))
```

---

## [1.8.0] - 2025-04-27 📝

### 🎯 主要更新

**WHERE 子句增强**
- ✨ **新增 WHERE 子句支持** - 支持在查询中添加原生 SQLAlchemy WHERE 条件
- ✨ **增强查询灵活性** - 结合 kwargs 和 whereclause 实现复杂查询

### 📚 相关 PR

- [#39](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/39) - 更新 1.7.0 版本更新日志
- [#40](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/40) - 添加 WHERE 子句支持到查询方法
- [#41](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/41) - 准备 1.8.0 版本发布

### 🚀 使用示例

```python
# 结合 WHERE 子句和 kwargs
users = await user_crud.select_models(
    session,
    User.created_at > datetime.now() - timedelta(days=30),  # WHERE 子句
    is_active=True,  # kwargs 条件
    name__like='%张%'
)
```

---

## [1.7.0] - 2025-04-13 📊

### 🎯 主要更新

**统计功能增强**
- ✨ **新增 count 和 exists 方法** - 提供便捷的统计和存在性检查功能
- ✨ **优化查询性能** - 使用专门的统计查询，避免不必要的数据传输

### 🔧 其他改进

- 📈 **更新项目链接** - 更新网站和仓库 URL
- 📈 **完善使用文档** - 添加 count 和 exists 的使用示例

### 📚 相关 PR

- [#34](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/34) - 更新网站和仓库 URL
- [#37](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/37) - 添加 count 和 exists 使用方法
- [#38](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/38) - 准备 1.7.0 版本发布

### 🚀 使用示例

```python
# 统计功能
total_users = await user_crud.count(session)
active_users = await user_crud.count(session, is_active=True)

# 存在性检查
email_exists = await user_crud.exists(session, email="<EMAIL>")
if not email_exists:
    # 创建新用户
    user = await user_crud.create_model(session, user_data)
```

---

## [1.6.0] - 2024-11-09 ⚡

### 🎯 主要更新

**事务控制增强**
- ✨ **新增 flush 参数支持** - 支持在事务中立即刷新数据到数据库
- ✨ **改进事务管理** - 提供更灵活的事务控制选项

### 📚 相关 PR

- [#32](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/32) - 添加 flush 使用方法
- [#33](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/33) - 准备 1.6.0 版本发布

### 🚀 使用示例

```python
# 使用 flush 获取自动生成的主键
async with session.begin():
    user = await user_crud.create_model(session, user_data, flush=True)
    # 此时 user.id 已可用，但事务未提交
    
    profile_data = ProfileCreate(user_id=user.id, bio="个人简介")
    profile = await profile_crud.create_model(session, profile_data)
    # 事务在 with 块结束时自动提交
```

---

## [1.5.0] - 2024-11-03 📚

### 🎯 主要更新

**文档和工具改进**
- ✨ **新增 kwargs 参数支持** - 在创建和更新操作中支持额外的关键字参数
- ✨ **包管理器更新** - 从 PDM 迁移到 UV，提升开发体验

### 🔧 其他改进

- 📈 **完善使用文档** - 更新各种使用场景的文档
- 📈 **更新交互链接** - 更新社区交流链接和描述

### 📚 相关 PR

- [#27](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/27) - 更新使用文档
- [#28](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/28) - 更新交互链接地址
- [#29](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/29) - 更新交互链接描述
- [#30](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/30) - 添加创建和更新的 kwargs 使用方法
- [#31](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/31) - 包管理器从 PDM 更新到 UV

### 🚀 使用示例

```python
# 使用 kwargs 添加额外字段
user = await user_crud.create_model(
    session, 
    user_data, 
    created_by="admin",  # 额外字段
    source="web"
)

# 更新时添加额外字段
await user_crud.update_model(
    session,
    pk=1,
    obj=update_data,
    updated_by="admin",  # 额外字段
    last_modified=datetime.now()
)
```

---

## [1.4.0] - 2024-08-27 🔧

### 🎯 主要更新

**主键处理优化**
- ✨ **动态主键检索** - 自动识别和处理模型的主键字段
- ✨ **增强兼容性** - 支持更多类型的主键定义

### 🔧 开发改进

- 📈 **添加 CI/CD** - 新增代码检查和测试的持续集成
- 📈 **完善测试** - 提高测试覆盖率和质量

### 👥 新贡献者

- [@DavidSche](https://github.com/DavidSche) 首次贡献 🎉

### 📚 相关 PR

- [#23](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/23) - 更新模型主键动态检索
- [#24](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/24) - 添加代码检查和测试 CI
- [#25](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/25) - 修复文档 CI 缓存
- [#26](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/26) - 准备 1.4.0 版本发布

---

## [1.3.0] - 2024-08-25 🔍

### 🎯 主要更新

**过滤功能增强**
- ✨ **新增 OR 过滤器** - 支持 `__or__` 和 `__gor__` 过滤条件
- ✨ **复杂查询支持** - 实现更灵活的条件组合查询

### 📚 相关 PR

- [#21](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/21) - 添加更多 OR 和 __gor__ 过滤器
- [#22](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/22) - 准备 1.3.0 版本发布

---

## [1.2.0] - 2024-08-24 🏗️

### 🎯 主要更新

**查询构建器增强**
- ✨ **新增查询和排序构建器** - 提供更灵活的查询构建方式
- ✨ **改进 API 设计** - 统一查询接口，提升开发体验

### 📚 相关 PR

- [#19](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/19) - 添加查询和排序构建器
- [#20](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/20) - 准备 1.2.0 版本发布

---

## [1.1.0] - 2024-08-24 📖

### 🎯 主要更新

**文档系统建立**
- ✨ **新增 MkDocs 文档** - 建立完整的使用文档系统
- ✨ **CI/CD 改进** - 添加文档构建和部署的自动化流程

### 🔧 技术改进

- 📈 **异步函数定义更新** - 优化异步函数的定义和使用
- 📈 **CI 流程完善** - 修复和改进持续集成流程

### 📚 相关 PR

- [#12](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/12) - 添加 MkDocs 使用文档
- [#13](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/13) - 更新异步函数定义
- [#14](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/14) - 更新文档构建 CI
- [#15](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/15) - 修复 CI 安装 pngquant 用户
- [#16](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/16) - 修复文档 CI 运行 pip
- [#17](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/17) - 准备 1.1.0 版本发布
- [#18](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/18) - 添加变更日志 CI

---

## [1.0.0] - 2024-08-12 🎉

### 🎯 重大里程碑

**CRUD 方法重构**
- 🚨 **重大重构** - CRUD 方法进行了全面重构，请查看文档了解新的使用方法
- ✨ **增强选择和操作** - 提供更强大和灵活的数据操作能力

### ⚠️ 破坏性变更

这是一个重大版本更新，包含破坏性变更。请在升级前仔细阅读新的文档和使用指南。

### 📚 相关 PR

- [#9](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/9) - 重构代码以增强选择和操作
- [#11](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/11) - 准备 1.0.0 版本发布

---

## [0.0.4] - 2024-08-09 🔧

### 🎯 主要更新

**类型支持和提交选项**
- ✨ **新增 PEP 561 支持** - 为库添加类型支持
- ✨ **新增提交选项** - 在 CRUD 操作中添加提交选项

### 📚 相关 PR

- [#7](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/7) - 为库添加 PEP 561 支持
- [#8](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/8) - 为 CRUD 操作添加提交选项
- [#10](https://github.com/fastapi-practices/sqlalchemy-crud-plus/pull/10) - 准备 0.0.4 版本发布

---

## 📊 版本统计

### 🎯 发布节奏
- **总版本数**: 10+ 个版本
- **发布周期**: 平均每月 1-2 个版本
- **稳定性**: 从 1.0.0 开始 API 趋于稳定

### 🚀 主要里程碑
- **v0.0.4**: 首个稳定的早期版本
- **v1.0.0**: 重大重构，API 设计成熟
- **v1.4.0**: 社区贡献开始增加
- **v1.7.0**: 功能完善，统计功能加入
- **v1.10.0**: 关系查询支持，功能趋于完整

### 📈 功能演进
1. **基础 CRUD** (v0.0.4 - v1.0.0)
2. **查询增强** (v1.1.0 - v1.3.0)
3. **性能优化** (v1.4.0 - v1.6.0)
4. **功能完善** (v1.7.0 - v1.9.0)
5. **关系查询** (v1.10.0+)

---

## 🔗 相关链接

- **GitHub 仓库**: [fastapi-practices/sqlalchemy-crud-plus](https://github.com/fastapi-practices/sqlalchemy-crud-plus)
- **完整变更日志**: [GitHub Releases](https://github.com/fastapi-practices/sqlalchemy-crud-plus/releases)
- **文档网站**: [SQLAlchemy CRUD Plus 文档](https://fastapi-practices.github.io/sqlalchemy-crud-plus)
- **社区交流**: [Discord](https://wu-clan.github.io/homepage/)

感谢所有贡献者和用户的支持！🙏

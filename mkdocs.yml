site_name: SQLAlchemy CRUD Plus
site_description: 现代化的 SQLAlchemy 2.0 异步 CRUD 操作库，为 Python 开发者提供简洁、强大、类型安全的数据库操作体验
site_url: https://fastapi-practices.github.io/sqlalchemy-crud-plus
site_author: FastAPI Practices
repo_name: sqlalchemy-crud-plus
repo_url: https://github.com/fastapi-practices/sqlalchemy-crud-plus

nav:
  - 🏠 首页: index.md
  - 🧭 文档导航: navigation.md
  - 📦 安装指南: installing.md
  - 🚀 快速开始:
      - 5分钟上手: getting-started/quick-start.md
  - 📖 基础用法:
      - CRUD 操作: usage/crud.md
  - 🔗 关系查询:
      - 关系查询指南: relationships/overview.md
  - 🎯 高级功能:
      - 过滤条件详解: advanced/filter.md
      - 事务控制管理: advanced/transaction.md
  - 🏆 最佳实践:
      - 生产环境指南: best-practices/overview.md
      - 性能优化策略: best-practices/performance.md
  - 🔌 框架集成:
      - FastAPI 集成: integrations/fastapi.md
      - Django 迁移指南: integrations/django-style.md
  - 💡 实际应用:
      - 真实案例分析: examples/real-world-examples.md
  - 🛠️ 故障排除:
      - 错误处理调试: troubleshooting/errors-and-debugging.md
  - 📋 API 参考:
      - CRUDPlus 类: api/crud-plus.md
      - 类型定义: api/types.md
      - 错误类型: api/errors.md
  - 📝 项目信息:
      - 更新日志 (英文): changelog.md
      - 更新日志 (中文): changelog-zh.md

theme:
  name: material
  language: zh
  palette:
    - media: '(prefers-color-scheme)'
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode
    - media: '(prefers-color-scheme: light)'
      scheme: default
      primary: pink
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - media: '(prefers-color-scheme: dark)'
      scheme: slate
      primary: teal
      toggle:
        icon: material/brightness-4
        name: Switch to system preference
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - content.tabs.link
    - content.tooltips
    - header.autohide
    - navigation.expand
    - navigation.footer
    - navigation.indexes
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.progress
    - navigation.path
    - navigation.prune
    - navigation.sections
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    - toc.integrate

plugins:
  - search:
      lang: zh
      separator: '[\s\-,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  - mkdocstrings:
      handlers:
        python:
          options:
            docstring_style: sphinx
            filters: [ "!^_" ]
            parameter_headings: true
            show_root_heading: true
            show_root_full_path: false
            show_symbol_type_heading: true
            show_symbol_type_toc: true
            members_order: source
            separate_signature: true
            show_signature_annotations: true
            signature_crossrefs: true

markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - tables
  - toc:
      permalink: true
      title: 页面目录
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
      auto_title: true
      linenums: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: fastapi-practices
      repo: sqlalchemy-crud-plus
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      auto_append:
        - includes/abbreviations.md
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      combine_header_slug: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/fastapi-practices/sqlalchemy-crud-plus
      name: GitHub 仓库
    - icon: fontawesome/brands/discord
      link: https://wu-clan.github.io/homepage/
      name: Discord 社区
    - icon: fontawesome/solid/book
      link: https://fastapi-practices.github.io/sqlalchemy-crud-plus
      name: 在线文档
  version:
    provider: mike
    default: latest

extra_css:
  - 'extra/custom.css'

extra_javascript:
  - 'extra/custom.js'

# 版权信息
copyright: |
  &copy; 2024 - 2025 FastAPI Practices - 为爱发电

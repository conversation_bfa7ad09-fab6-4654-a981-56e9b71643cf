# 安装指南

本指南将详细介绍如何安装和配置 SQLAlchemy CRUD Plus，包括不同环境下的安装方法和常见问题解决方案。

## 📋 系统要求

### Python 版本要求

- **Python 3.10+** （推荐 Python 3.11+）
- 支持 asyncio 异步编程

### 核心依赖

- **SQLAlchemy 2.0+** - 现代 ORM 框架
- **Pydantic 2.0+** - 数据验证和序列化

### 数据库支持

SQLAlchemy CRUD Plus 支持所有 SQLAlchemy 2.0 兼容的数据库：

| 数据库 | 驱动包 | 连接字符串示例 |
|--------|--------|----------------|
| PostgreSQL | `asyncpg` | `postgresql+asyncpg://user:pass@localhost/db` |
| MySQL | `aiomysql` | `mysql+aiomysql://user:pass@localhost/db` |
| SQLite | `aiosqlite` | `sqlite+aiosqlite:///./database.db` |
| SQL Server | `aioodbc` | `mssql+aioodbc://user:pass@server/db` |
| Oracle | `cx_oracle_async` | `oracle+cx_oracle_async://user:pass@host/db` |

## 🚀 安装方法

### 基础安装

```bash
# 安装核心包
pip install sqlalchemy-crud-plus
```

### 完整安装（推荐）

根据您使用的数据库选择相应的驱动：

```bash
# PostgreSQL
pip install sqlalchemy-crud-plus[postgresql]
# 或手动安装
pip install sqlalchemy-crud-plus asyncpg

# MySQL
pip install sqlalchemy-crud-plus[mysql]
# 或手动安装
pip install sqlalchemy-crud-plus aiomysql

# SQLite（开发/测试）
pip install sqlalchemy-crud-plus[sqlite]
# 或手动安装
pip install sqlalchemy-crud-plus aiosqlite

# 所有数据库支持
pip install sqlalchemy-crud-plus[all]
```

### 开发环境安装

如果您需要参与开发或运行测试：

```bash
# 克隆仓库
git clone https://github.com/fastapi-practices/sqlalchemy-crud-plus.git
cd sqlalchemy-crud-plus

# 安装开发依赖
pip install -e ".[dev]"

# 或使用 poetry
poetry install --with dev
```

## 🔧 环境配置

### 1. 虚拟环境设置

**使用 venv（推荐）：**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装包
pip install sqlalchemy-crud-plus
```

**使用 conda：**
```bash
# 创建环境
conda create -n myproject python=3.11
conda activate myproject

# 安装包
pip install sqlalchemy-crud-plus
```

**使用 poetry：**
```bash
# 初始化项目
poetry init
poetry add sqlalchemy-crud-plus

# 激活环境
poetry shell
```

### 2. 项目配置文件

**requirements.txt：**
```txt
sqlalchemy-crud-plus>=1.10.0
asyncpg>=0.28.0          # PostgreSQL
# aiomysql>=0.2.0        # MySQL
# aiosqlite>=0.19.0      # SQLite
pydantic>=2.0.0
fastapi>=0.100.0         # 如果使用 FastAPI
```

**pyproject.toml（Poetry）：**
```toml
[tool.poetry.dependencies]
python = "^3.10"
sqlalchemy-crud-plus = "^1.10.0"
asyncpg = "^0.28.0"
pydantic = "^2.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-asyncio = "^0.21.0"
```

## ✅ 验证安装

### 1. 基础验证

```python
# test_installation.py
import asyncio
import sqlalchemy_crud_plus
from sqlalchemy_crud_plus import CRUDPlus

def test_import():
    """测试导入"""
    print(f"✅ SQLAlchemy CRUD Plus 版本: {sqlalchemy_crud_plus.__version__}")
    print("✅ 导入成功")

def test_crud_class():
    """测试 CRUD 类"""
    from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

    class Base(DeclarativeBase):
        pass

    class TestModel(Base):
        __tablename__ = 'test'
        id: Mapped[int] = mapped_column(primary_key=True)

    crud = CRUDPlus(TestModel)
    print("✅ CRUDPlus 类创建成功")
    print(f"✅ 模型: {crud.model.__name__}")

if __name__ == "__main__":
    test_import()
    test_crud_class()
    print("\n🎉 安装验证完成！")
```

运行验证：
```bash
python test_installation.py
```

### 2. 数据库连接测试

```python
# test_database.py
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy_crud_plus import CRUDPlus

# 配置数据库连接（根据实际情况修改）
DATABASE_URL = "sqlite+aiosqlite:///./test.db"

class Base(DeclarativeBase):
    pass

class TestUser(Base):
    __tablename__ = 'test_users'
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column()

async def test_database_connection():
    """测试数据库连接和基础操作"""
    # 创建引擎和会话
    engine = create_async_engine(DATABASE_URL, echo=True)
    async_session = async_sessionmaker(bind=engine, class_=AsyncSession)

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 测试 CRUD 操作
    user_crud = CRUDPlus(TestUser)

    async with async_session() as session:
        # 创建测试数据
        from pydantic import BaseModel

        class UserCreate(BaseModel):
            name: str

        user_data = UserCreate(name="测试用户")
        user = await user_crud.create_model(session, user_data)
        await session.commit()

        print(f"✅ 创建用户成功: {user.name} (ID: {user.id})")

        # 查询测试
        found_user = await user_crud.select_model(session, pk=user.id)
        print(f"✅ 查询用户成功: {found_user.name}")

        # 清理
        await user_crud.delete_model(session, pk=user.id)
        await session.commit()
        print("✅ 清理测试数据完成")

    await engine.dispose()
    print("🎉 数据库连接测试完成！")

if __name__ == "__main__":
    asyncio.run(test_database_connection())
```

## 🐛 常见问题

### 1. 导入错误

**问题：** `ModuleNotFoundError: No module named 'sqlalchemy_crud_plus'`

**解决方案：**
```bash
# 确认安装
pip list | grep sqlalchemy-crud-plus

# 重新安装
pip uninstall sqlalchemy-crud-plus
pip install sqlalchemy-crud-plus

# 检查 Python 路径
python -c "import sys; print(sys.path)"
```

### 2. 版本兼容性问题

**问题：** SQLAlchemy 版本不兼容

**解决方案：**
```bash
# 检查版本
pip show sqlalchemy

# 升级到 2.0+
pip install --upgrade sqlalchemy>=2.0.0

# 如果有冲突，使用虚拟环境
python -m venv fresh_env
source fresh_env/bin/activate  # Linux/Mac
# 或 fresh_env\Scripts\activate  # Windows
pip install sqlalchemy-crud-plus
```

### 3. 数据库驱动问题

**问题：** 数据库连接失败

**解决方案：**
```bash
# PostgreSQL
pip install asyncpg
# 测试连接
python -c "import asyncpg; print('AsyncPG 可用')"

# MySQL
pip install aiomysql
# 测试连接
python -c "import aiomysql; print('AioMySQL 可用')"

# SQLite
pip install aiosqlite
# 测试连接
python -c "import aiosqlite; print('AioSQLite 可用')"
```

### 4. 异步环境问题

**问题：** `RuntimeError: asyncio.run() cannot be called from a running event loop`

**解决方案：**
```python
# 在 Jupyter Notebook 中使用
import nest_asyncio
nest_asyncio.apply()

# 或使用 await 而不是 asyncio.run()
# await your_async_function()
```

## 🔄 升级指南

### 从旧版本升级

```bash
# 检查当前版本
pip show sqlalchemy-crud-plus

# 升级到最新版本
pip install --upgrade sqlalchemy-crud-plus

# 检查升级后版本
pip show sqlalchemy-crud-plus
```

### 版本兼容性检查

```python
# version_check.py
import sqlalchemy_crud_plus
import sqlalchemy
import pydantic

def check_versions():
    print("📦 依赖版本检查:")
    print(f"  SQLAlchemy CRUD Plus: {sqlalchemy_crud_plus.__version__}")
    print(f"  SQLAlchemy: {sqlalchemy.__version__}")
    print(f"  Pydantic: {pydantic.__version__}")

    # 版本要求检查
    from packaging import version

    sqlalchemy_version = version.parse(sqlalchemy.__version__)
    pydantic_version = version.parse(pydantic.__version__)

    if sqlalchemy_version >= version.parse("2.0.0"):
        print("  ✅ SQLAlchemy 版本符合要求")
    else:
        print("  ❌ SQLAlchemy 版本过低，需要 2.0+")

    if pydantic_version >= version.parse("2.0.0"):
        print("  ✅ Pydantic 版本符合要求")
    else:
        print("  ❌ Pydantic 版本过低，需要 2.0+")

if __name__ == "__main__":
    check_versions()
```

## 🎯 下一步

安装完成后，您可以：

1. **快速上手**：查看 [快速开始指南](getting-started/quick-start.md)
2. **深入学习**：阅读 [基础用法文档](usage/crud.md)
3. **实际应用**：探索 [实际应用示例](examples/real-world-examples.md)
4. **框架集成**：了解 [FastAPI 集成](integrations/fastapi.md)
5. **最佳实践**：学习 [生产环境指南](best-practices/overview.md)

## 📞 获取帮助

如果在安装过程中遇到问题：

1. **查看文档**：[故障排除指南](troubleshooting/errors-and-debugging.md)
2. **GitHub Issues**：[提交问题](https://github.com/fastapi-practices/sqlalchemy-crud-plus/issues)
3. **社区讨论**：[Discord 频道](https://wu-clan.github.io/homepage/)

祝您使用愉快！🎉

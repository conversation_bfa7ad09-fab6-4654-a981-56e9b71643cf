# 快速开始

本指南将帮助您在 5 分钟内上手 SQLAlchemy CRUD Plus，从零开始构建一个完整的数据操作示例。

## 📦 环境准备

### 安装依赖

```bash
# 安装 SQLAlchemy CRUD Plus
pip install sqlalchemy-crud-plus

# 安装数据库驱动（选择其一）
pip install asyncpg          # PostgreSQL
pip install aiomysql         # MySQL
pip install aiosqlite        # SQLite
```

### 项目结构

```
my_project/
├── main.py              # 主程序文件
├── models.py            # 数据模型定义
├── schemas.py           # Pydantic 模式定义
└── database.py          # 数据库配置
```

## 🏗️ 基础设置

### 1. 数据库配置

```python
# database.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import DeclarativeBase

# 数据库连接 URL（根据实际情况修改）
DATABASE_URL = "sqlite+aiosqlite:///./test.db"
# DATABASE_URL = "postgresql+asyncpg://user:password@localhost/dbname"
# DATABASE_URL = "mysql+aiomysql://user:password@localhost/dbname"

# 创建异步引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # 开发环境显示 SQL 语句
    future=True
)

# 创建会话工厂
async_session = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 基础模型类
class Base(DeclarativeBase):
    pass

# 获取数据库会话
async def get_session():
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()

# 创建数据表
async def create_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
```

### 2. 数据模型定义

```python
# models.py
from __future__ import annotations
from datetime import datetime
from sqlalchemy import ForeignKey, String, DateTime, Text, Boolean, func
from sqlalchemy.orm import Mapped, mapped_column, relationship
from database import Base


class User(Base):
    """用户模型"""
    __tablename__ = 'users'

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(50), index=True, comment="用户名")
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment="邮箱")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")

    # 时间戳字段
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 关系定义
    posts: Mapped[list[Post]] = relationship(
        back_populates="author",
        cascade="all, delete-orphan"
    )
    profile: Mapped[UserProfile] = relationship(
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<User(id={self.id}, name='{self.name}', email='{self.email}')>"


class UserProfile(Base):
    """用户资料模型"""
    __tablename__ = 'user_profiles'

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey('users.id'), unique=True)
    bio: Mapped[str] = mapped_column(Text, nullable=True, comment="个人简介")
    avatar_url: Mapped[str] = mapped_column(String(255), nullable=True, comment="头像URL")

    # 关系
    user: Mapped[User] = relationship(back_populates="profile")


class Post(Base):
    """文章模型"""
    __tablename__ = 'posts'

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(200), index=True, comment="标题")
    content: Mapped[str] = mapped_column(Text, comment="内容")
    is_published: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否发布")
    author_id: Mapped[int] = mapped_column(ForeignKey('users.id'), index=True)

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    author: Mapped[User] = relationship(back_populates="posts")

    def __repr__(self):
        return f"<Post(id={self.id}, title='{self.title}', author_id={self.author_id})>"
```

### 3. Pydantic 模式定义

```python
# schemas.py
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, ConfigDict


# 用户相关模式
class UserBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """用户创建模式"""
    pass


class UserUpdate(BaseModel):
    """用户更新模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """用户响应模式"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime
    updated_at: datetime


# 用户资料相关模式
class UserProfileBase(BaseModel):
    bio: Optional[str] = Field(None, max_length=1000, description="个人简介")
    avatar_url: Optional[str] = Field(None, description="头像URL")


class UserProfileCreate(UserProfileBase):
    user_id: int


class UserProfileUpdate(UserProfileBase):
    pass


class UserProfileResponse(UserProfileBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    user_id: int


# 文章相关模式
class PostBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="文章标题")
    content: str = Field(..., min_length=1, description="文章内容")
    is_published: bool = Field(default=False, description="是否发布")


class PostCreate(PostBase):
    author_id: int


class PostUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    is_published: Optional[bool] = None


class PostResponse(PostBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    author_id: int
    created_at: datetime
    updated_at: datetime


# 包含关系的复合模式
class UserWithProfile(UserResponse):
    """包含资料的用户模式"""
    profile: Optional[UserProfileResponse] = None


class UserWithPosts(UserResponse):
    """包含文章的用户模式"""
    posts: List[PostResponse] = []


class PostWithAuthor(PostResponse):
    """包含作者的文章模式"""
    author: UserResponse
```

## 🚀 开始使用 CRUD Plus

### 1. 创建 CRUD 实例

```python
# main.py
import asyncio
from sqlalchemy_crud_plus import CRUDPlus
from database import create_tables, get_session
from models import User, Post, UserProfile
from schemas import UserCreate, UserUpdate, PostCreate, UserProfileCreate

# 创建 CRUD 实例
user_crud = CRUDPlus(User)
post_crud = CRUDPlus(Post)
profile_crud = CRUDPlus(UserProfile)


async def main():
    """主函数演示所有功能"""
    # 创建数据表
    await create_tables()

    # 获取数据库会话
    async for session in get_session():
        # 在这里执行所有数据库操作
        await demo_crud_operations(session)
        break  # 只执行一次


async def demo_crud_operations(session):
    """演示 CRUD 操作"""
    print("🚀 开始演示 SQLAlchemy CRUD Plus 功能\n")

    # === 创建操作演示 ===
    await demo_create_operations(session)

    # === 查询操作演示 ===
    await demo_read_operations(session)

    # === 更新操作演示 ===
    await demo_update_operations(session)

    # === 关系查询演示 ===
    await demo_relationship_operations(session)

    # === 高级查询演示 ===
    await demo_advanced_queries(session)


if __name__ == "__main__":
    asyncio.run(main())
```

### 2. 创建操作（Create）

```python
async def demo_create_operations(session):
    """演示创建操作"""
    print("📝 === 创建操作演示 ===")

    # 1. 创建单个用户
    print("1. 创建单个用户")
    user_data = UserCreate(
        name="张三",
        email="<EMAIL>",
        is_active=True
    )
    user = await user_crud.create_model(session, user_data)
    print(f"   创建用户成功: {user.name} (ID: {user.id})")

    # 2. 批量创建用户
    print("\n2. 批量创建用户")
    users_data = [
        UserCreate(name="李四", email="<EMAIL>"),
        UserCreate(name="王五", email="<EMAIL>"),
        UserCreate(name="赵六", email="<EMAIL>")
    ]
    users = await user_crud.create_models(session, users_data)
    print(f"   批量创建 {len(users)} 个用户成功")

    # 3. 创建用户资料（演示关系数据）
    print("\n3. 创建用户资料")
    profile_data = UserProfileCreate(
        user_id=user.id,
        bio="这是张三的个人简介",
        avatar_url="https://example.com/avatar.jpg"
    )
    profile = await profile_crud.create_model(session, profile_data)
    print(f"   为用户 {user.name} 创建资料成功")

    # 4. 创建文章
    print("\n4. 创建文章")
    post_data = PostCreate(
        title="我的第一篇文章",
        content="这是文章的内容...",
        author_id=user.id,
        is_published=True
    )
    post = await post_crud.create_model(session, post_data)
    print(f"   创建文章成功: {post.title} (ID: {post.id})")

    # 5. 使用事务控制
    print("\n5. 事务控制演示")
    async with session.begin():
        # 在事务中创建多个相关记录
        user_data = UserCreate(name="事务用户", email="<EMAIL>")
        new_user = await user_crud.create_model(session, user_data, flush=True)

        # 使用 flush=True 立即获取主键，但不提交事务
        post_data = PostCreate(
            title="事务中的文章",
            content="这篇文章在事务中创建",
            author_id=new_user.id
        )
        new_post = await post_crud.create_model(session, post_data)

        print(f"   在事务中创建用户和文章成功")
        # 事务会在 with 块结束时自动提交

    print("✅ 创建操作演示完成\n")


async def demo_read_operations(session):
    """演示查询操作"""
    print("🔍 === 查询操作演示 ===")

    # 1. 主键查询
    print("1. 主键查询")
    user = await user_crud.select_model(session, pk=1)
    if user:
        print(f"   查询到用户: {user.name} ({user.email})")

    # 2. 条件查询单个记录
    print("\n2. 条件查询单个记录")
    user = await user_crud.select_model_by_column(
        session,
        email="<EMAIL>"
    )
    if user:
        print(f"   根据邮箱查询到用户: {user.name}")

    # 3. 查询多个记录
    print("\n3. 查询多个记录")
    users = await user_crud.select_models(session, is_active=True)
    print(f"   查询到 {len(users)} 个活跃用户")

    # 4. 分页查询
    print("\n4. 分页查询")
    users = await user_crud.select_models(
        session,
        is_active=True,
        limit=2,
        offset=0
    )
    print(f"   分页查询到 {len(users)} 个用户")

    # 5. 排序查询
    print("\n5. 排序查询")
    users = await user_crud.select_models_order(
        session,
        sort_columns="created_at",
        sort_orders="desc",
        limit=3
    )
    print(f"   按创建时间倒序查询到 {len(users)} 个用户")
    for user in users:
        print(f"     - {user.name} (创建于: {user.created_at})")

    # 6. 计数和存在性检查
    print("\n6. 计数和存在性检查")
    total_users = await user_crud.count(session)
    active_users = await user_crud.count(session, is_active=True)
    email_exists = await user_crud.exists(session, email="<EMAIL>")

    print(f"   总用户数: {total_users}")
    print(f"   活跃用户数: {active_users}")
    print(f"   邮箱 <EMAIL> 是否存在: {email_exists}")

    print("✅ 查询操作演示完成\n")


async def demo_update_operations(session):
    """演示更新操作"""
    print("✏️ === 更新操作演示 ===")

    # 1. 主键更新
    print("1. 主键更新")
    update_data = UserUpdate(name="张三丰")
    updated_count = await user_crud.update_model(session, pk=1, obj=update_data)
    print(f"   更新了 {updated_count} 个用户的姓名")

    # 2. 使用字典更新
    print("\n2. 使用字典更新")
    updated_count = await user_crud.update_model(
        session,
        pk=1,
        obj={"is_active": True}
    )
    print(f"   更新了 {updated_count} 个用户的状态")

    # 3. 条件批量更新
    print("\n3. 条件批量更新")
    updated_count = await user_crud.update_model_by_column(
        session,
        obj={"is_active": True},
        name__like="%李%"
    )
    print(f"   批量更新了 {updated_count} 个姓李的用户")

    # 4. 复杂条件更新
    print("\n4. 复杂条件更新")
    from datetime import datetime, timedelta

    # 更新最近创建的用户
    recent_time = datetime.now() - timedelta(minutes=10)
    updated_count = await user_crud.update_model_by_column(
        session,
        obj={"is_active": True},
        created_at__gte=recent_time
    )
    print(f"   更新了 {updated_count} 个最近创建的用户")

    print("✅ 更新操作演示完成\n")
```

async def demo_relationship_operations(session):
    """演示关系查询操作"""
    print("🔗 === 关系查询演示 ===")

    # 1. 预加载关系数据
    print("1. 预加载关系数据")
    user = await user_crud.select_model(
        session,
        pk=1,
        load_strategies=['posts', 'profile']
    )
    if user:
        print(f"   用户 {user.name}:")
        print(f"     - 文章数量: {len(user.posts)}")
        print(f"     - 有个人资料: {user.profile is not None}")
        if user.posts:
            for post in user.posts:
                print(f"       * {post.title}")

    # 2. 指定加载策略
    print("\n2. 指定加载策略")
    users = await user_crud.select_models(
        session,
        load_strategies={
            'posts': 'selectinload',    # 一对多关系用 selectinload
            'profile': 'joinedload'     # 一对一关系用 joinedload
        },
        limit=2
    )
    print(f"   使用不同策略加载了 {len(users)} 个用户的关系数据")

    # 3. 嵌套关系加载
    print("\n3. 嵌套关系加载")
    posts = await post_crud.select_models(
        session,
        load_strategies={
            'author': 'joinedload',
            'author.profile': 'joinedload'
        },
        limit=3
    )
    print(f"   加载了 {len(posts)} 篇文章及其作者信息")
    for post in posts:
        print(f"     - {post.title} (作者: {post.author.name})")

    # 4. JOIN 查询
    print("\n4. JOIN 查询")
    # 只返回有文章的用户
    users_with_posts = await user_crud.select_models(
        session,
        join_conditions=['posts']
    )
    print(f"   找到 {len(users_with_posts)} 个有文章的用户")

    # 5. 指定 JOIN 类型
    print("\n5. 指定 JOIN 类型")
    users = await user_crud.select_models(
        session,
        join_conditions={
            'posts': 'left',      # LEFT JOIN
            'profile': 'inner'    # INNER JOIN
        }
    )
    print(f"   使用不同 JOIN 类型查询到 {len(users)} 个用户")

    print("✅ 关系查询演示完成\n")


async def demo_advanced_queries(session):
    """演示高级查询功能"""
    print("🎯 === 高级查询演示 ===")

    # 1. 复杂过滤条件
    print("1. 复杂过滤条件")
    users = await user_crud.select_models(
        session,
        name__like='%张%',           # 名字包含"张"
        is_active=True,              # 且是活跃用户
        created_at__gte='2024-01-01' # 且在2024年后创建
    )
    print(f"   复杂条件查询到 {len(users)} 个用户")

    # 2. OR 条件查询
    print("\n2. OR 条件查询")
    users = await user_crud.select_models(
        session,
        __or__={
            'name__like': '%张%',
            'name__like': '%李%',
            'email__endswith': '@example.com'
        }
    )
    print(f"   OR 条件查询到 {len(users)} 个用户")

    # 3. 范围查询
    print("\n3. 范围查询")
    from datetime import datetime, timedelta

    recent_time = datetime.now() - timedelta(days=1)
    users = await user_crud.select_models(
        session,
        id__in=[1, 2, 3],                    # ID 在指定列表中
        created_at__between=[recent_time, datetime.now()]  # 创建时间在范围内
    )
    print(f"   范围查询到 {len(users)} 个用户")

    # 4. 字符串操作
    print("\n4. 字符串操作")
    users = await user_crud.select_models(
        session,
        email__startswith='zhang',    # 邮箱以 zhang 开头
        name__endswith='三'           # 名字以"三"结尾
    )
    print(f"   字符串操作查询到 {len(users)} 个用户")

    # 5. 聚合查询
    print("\n5. 聚合查询")
    # 统计每个用户的文章数量
    users_with_posts = await user_crud.select_models(
        session,
        load_strategies=['posts']
    )

    user_post_counts = []
    for user in users_with_posts:
        user_post_counts.append({
            'user_name': user.name,
            'post_count': len(user.posts)
        })

    print("   用户文章统计:")
    for item in user_post_counts:
        print(f"     - {item['user_name']}: {item['post_count']} 篇文章")

    # 6. 删除操作演示
    print("\n6. 删除操作演示")

    # 创建一个测试用户用于删除
    test_user_data = UserCreate(
        name="测试删除用户",
        email="<EMAIL>"
    )
    test_user = await user_crud.create_model(session, test_user_data)
    print(f"   创建测试用户: {test_user.name} (ID: {test_user.id})")

    # 主键删除
    deleted_count = await user_crud.delete_model(session, pk=test_user.id)
    print(f"   删除了 {deleted_count} 个用户")

    # 条件删除（演示，实际不执行）
    print("   条件删除示例（未实际执行）:")
    print("     await user_crud.delete_model_by_column(session, is_active=False)")

    print("✅ 高级查询演示完成\n")


# 完整的运行示例
async def complete_example():
    """完整的使用示例"""
    print("🎉 === 完整使用示例 ===")

    # 创建数据表
    await create_tables()

    async for session in get_session():
        try:
            # 1. 创建用户和相关数据
            print("1. 创建博客系统数据")

            # 创建作者
            author_data = UserCreate(
                name="技术博主",
                email="<EMAIL>"
            )
            author = await user_crud.create_model(session, author_data, flush=True)

            # 创建作者资料
            profile_data = UserProfileCreate(
                user_id=author.id,
                bio="专注于 Python 和 Web 开发的技术博主",
                avatar_url="https://example.com/blogger-avatar.jpg"
            )
            await profile_crud.create_model(session, profile_data)

            # 创建多篇文章
            posts_data = [
                PostCreate(
                    title="SQLAlchemy CRUD Plus 入门指南",
                    content="这是一篇关于 SQLAlchemy CRUD Plus 的详细教程...",
                    author_id=author.id,
                    is_published=True
                ),
                PostCreate(
                    title="Python 异步编程最佳实践",
                    content="本文介绍了 Python 异步编程的最佳实践...",
                    author_id=author.id,
                    is_published=True
                ),
                PostCreate(
                    title="数据库设计原则",
                    content="这篇文章讨论了数据库设计的基本原则...",
                    author_id=author.id,
                    is_published=False  # 草稿状态
                )
            ]

            posts = await post_crud.create_models(session, posts_data)
            print(f"   创建了作者 {author.name} 和 {len(posts)} 篇文章")

            # 2. 查询和展示数据
            print("\n2. 查询博客数据")

            # 获取作者及其所有文章和资料
            author_with_data = await user_crud.select_model(
                session,
                pk=author.id,
                load_strategies={
                    'posts': 'selectinload',
                    'profile': 'joinedload'
                }
            )

            print(f"   作者信息: {author_with_data.name}")
            print(f"   个人简介: {author_with_data.profile.bio}")
            print(f"   文章列表:")

            for post in author_with_data.posts:
                status = "已发布" if post.is_published else "草稿"
                print(f"     - {post.title} ({status})")

            # 3. 统计信息
            print("\n3. 统计信息")

            total_users = await user_crud.count(session)
            total_posts = await post_crud.count(session)
            published_posts = await post_crud.count(session, is_published=True)

            print(f"   总用户数: {total_users}")
            print(f"   总文章数: {total_posts}")
            print(f"   已发布文章: {published_posts}")

            print("\n🎉 完整示例演示完成!")

        except Exception as e:
            print(f"❌ 发生错误: {e}")
            await session.rollback()
            raise

        break  # 只执行一次


# 如果直接运行此文件
if __name__ == "__main__":
    print("🚀 SQLAlchemy CRUD Plus 快速开始演示")
    print("=" * 50)

    # 运行完整示例
    asyncio.run(complete_example())
```

## 🎯 核心概念总结

通过上面的示例，您已经学会了 SQLAlchemy CRUD Plus 的核心功能：

### ✅ 已掌握的功能

1. **基础 CRUD 操作**
   - ✅ 创建记录（单个和批量）
   - ✅ 查询记录（主键、条件、分页、排序）
   - ✅ 更新记录（主键和条件更新）
   - ✅ 删除记录（主键和条件删除）

2. **关系查询**
   - ✅ 预加载关系数据
   - ✅ 指定加载策略
   - ✅ 嵌套关系加载
   - ✅ JOIN 查询

3. **高级功能**
   - ✅ 复杂过滤条件
   - ✅ OR 条件查询
   - ✅ 事务控制
   - ✅ 统计和聚合

### 🎓 下一步学习

- [📝 基础用法详解](../usage/crud.md) - 深入了解每个 CRUD 操作的细节
- [🔗 关系查询进阶](../relationships/overview.md) - 掌握复杂的关系查询技巧
- [🎯 高级过滤功能](../advanced/filter.md) - 学习 34+ 种过滤操作符
- [⚡ 事务控制](../advanced/transaction.md) - 掌握事务管理和性能优化
- [🚀 FastAPI 集成](../integrations/fastapi.md) - 在 Web 应用中使用
- [🏆 最佳实践](../best-practices/overview.md) - 生产环境使用指南

### 💡 小贴士

1. **开发环境**：设置 `echo=True` 查看生成的 SQL 语句
2. **类型提示**：充分利用 IDE 的智能提示功能
3. **错误处理**：使用 try-catch 处理数据库操作异常
4. **性能优化**：合理使用预加载策略避免 N+1 查询
5. **事务管理**：在复杂操作中使用事务确保数据一致性

恭喜！您已经掌握了 SQLAlchemy CRUD Plus 的基础用法。现在可以开始构建您的应用了！ 🎉

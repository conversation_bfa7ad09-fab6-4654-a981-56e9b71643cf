# 性能优化指南

本指南详细介绍如何优化 SQLAlchemy CRUD Plus 的性能，确保应用在生产环境中高效运行。

## 🎯 性能优化概览

### 性能瓶颈识别

常见的性能问题及其解决方案：

| 问题类型 | 症状 | 解决方案 |
|---------|------|---------|
| N+1 查询 | 大量重复查询 | 使用预加载策略 |
| 过度加载 | 内存占用过高 | 选择性字段加载 |
| 长事务 | 数据库锁等待 | 优化事务范围 |
| 缺少索引 | 查询缓慢 | 添加数据库索引 |
| 批量操作低效 | 单条操作循环 | 使用批量方法 |

## 🔍 查询优化

### 1. 预加载策略选择

根据关系类型选择最优的预加载策略：

```python
# ✅ 一对一关系：使用 joinedload
user = await user_crud.select_model(
    session,
    pk=user_id,
    load_strategies={'profile': 'joinedload'}
)

# ✅ 一对多关系：使用 selectinload
user = await user_crud.select_model(
    session,
    pk=user_id,
    load_strategies={'posts': 'selectinload'}
)

# ✅ 多对多关系：使用 selectinload 或 subqueryload
user = await user_crud.select_model(
    session,
    pk=user_id,
    load_strategies={'roles': 'selectinload'}
)

# ❌ 错误：对一对多关系使用 joinedload（会产生笛卡尔积）
user = await user_crud.select_model(
    session,
    pk=user_id,
    load_strategies={'posts': 'joinedload'}  # 避免这样做
)
```

### 2. 嵌套关系优化

```python
# ✅ 高效的嵌套关系加载
users = await user_crud.select_models(
    session,
    load_strategies={
        'posts': 'selectinload',
        'posts.comments': 'selectinload',
        'posts.category': 'joinedload',
        'profile': 'joinedload'
    },
    limit=20
)

# ❌ 避免：过深的嵌套关系
users = await user_crud.select_models(
    session,
    load_strategies={
        'posts': 'selectinload',
        'posts.comments': 'selectinload',
        'posts.comments.author': 'selectinload',
        'posts.comments.author.profile': 'joinedload',
        # 过深的嵌套会影响性能
    }
)
```

### 3. 选择性字段加载

```python
from sqlalchemy.orm import load_only

# ✅ 只加载需要的字段
users = await user_crud.select_models(
    session,
    load_options=[load_only(User.id, User.name, User.email)],
    limit=100
)

# ✅ 延迟加载大字段
from sqlalchemy.orm import defer

users = await user_crud.select_models(
    session,
    load_options=[defer(User.large_text_field)],
    limit=100
)
```

## 📊 批量操作优化

### 1. 批量创建

```python
# ✅ 高效的批量创建
users_data = [
    UserCreate(name=f"用户{i}", email=f"user{i}@example.com")
    for i in range(1000)
]

# 方法1：使用 create_models（推荐）
users = await user_crud.create_models(session, users_data)

# 方法2：使用 create_models_by_dict（更高性能）
users_dict = [user.model_dump() for user in users_data]
users = await user_crud.create_models_by_dict(session, users_dict)

# ❌ 避免：循环单个创建
for user_data in users_data:
    await user_crud.create_model(session, user_data)  # 低效
```

### 2. 批量更新

```python
# ✅ 条件批量更新
updated_count = await user_crud.update_model_by_column(
    session,
    obj={'is_active': True, 'updated_at': datetime.now()},
    created_at__gte=datetime.now() - timedelta(days=30)
)

# ✅ 分批处理大量数据
async def batch_update_users(session: AsyncSession, updates: List[Dict]):
    batch_size = 1000
    for i in range(0, len(updates), batch_size):
        batch = updates[i:i + batch_size]
        
        async with session.begin():
            for update_data in batch:
                await user_crud.update_model(
                    session,
                    pk=update_data['id'],
                    obj=update_data['data']
                )
```

### 3. 批量删除

```python
# ✅ 条件批量删除
deleted_count = await user_crud.delete_model_by_column(
    session,
    is_active=False,
    created_at__lt=datetime.now() - timedelta(days=365)
)

# ✅ 软删除批量操作
updated_count = await user_crud.update_model_by_column(
    session,
    obj={'is_deleted': True, 'deleted_at': datetime.now()},
    is_active=False
)
```

## ⚡ 事务优化

### 1. 事务范围控制

```python
# ✅ 合理的事务范围
async def create_user_with_profile(
    session: AsyncSession,
    user_data: UserCreate,
    profile_data: ProfileCreate
):
    async with session.begin():
        # 创建用户并获取ID
        user = await user_crud.create_model(session, user_data, flush=True)
        
        # 创建关联的个人资料
        profile_data.user_id = user.id
        profile = await profile_crud.create_model(session, profile_data)
        
        return user, profile

# ❌ 避免：过长的事务
async def bad_transaction_example(session: AsyncSession):
    async with session.begin():
        # 大量操作
        users = await user_crud.select_models(session, limit=10000)
        
        # 长时间处理
        for user in users:
            # 复杂的业务逻辑处理
            await process_user_data(user)  # 可能很耗时
            
        # 更多数据库操作
        # ... 事务持续时间过长
```

### 2. 并发事务处理

```python
import asyncio

# ✅ 并发处理独立事务
async def process_users_concurrently(user_ids: List[int]):
    async def process_single_user(user_id: int):
        async with db_manager.get_session() as session:
            async with session.begin():
                user = await user_crud.select_model(session, pk=user_id)
                # 处理单个用户
                await update_user_stats(session, user)
    
    # 并发处理
    tasks = [process_single_user(user_id) for user_id in user_ids]
    await asyncio.gather(*tasks, return_exceptions=True)
```

### 3. 保存点使用

```python
# ✅ 使用保存点处理部分失败
async def create_user_with_optional_profile(
    session: AsyncSession,
    user_data: UserCreate,
    profile_data: ProfileCreate = None
):
    async with session.begin():
        # 创建用户（必须成功）
        user = await user_crud.create_model(session, user_data, flush=True)
        
        if profile_data:
            # 创建保存点
            savepoint = await session.begin_nested()
            try:
                profile_data.user_id = user.id
                profile = await profile_crud.create_model(session, profile_data)
                await savepoint.commit()
            except Exception as e:
                # 回滚到保存点，但保留用户创建
                await savepoint.rollback()
                logger.warning(f"创建用户资料失败: {e}")
        
        return user
```

## 🗃️ 数据库优化

### 1. 索引策略

```python
# 在模型中定义索引
from sqlalchemy import Index

class User(Base):
    __tablename__ = 'users'
    
    id: Mapped[int] = mapped_column(primary_key=True)
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    name: Mapped[str] = mapped_column(String(50), index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, index=True)
    is_active: Mapped[bool] = mapped_column(default=True, index=True)
    
    # 复合索引
    __table_args__ = (
        Index('idx_user_email_active', 'email', 'is_active'),
        Index('idx_user_created_active', 'created_at', 'is_active'),
    )

# 常用查询的索引优化
users = await user_crud.select_models(
    session,
    is_active=True,  # 使用 is_active 索引
    created_at__gte=datetime.now() - timedelta(days=30),  # 使用复合索引
    limit=20
)
```

### 2. 查询优化技巧

```python
# ✅ 使用 exists 检查存在性
user_exists = await user_crud.exists(session, email="<EMAIL>")

# ❌ 避免：使用 count 检查存在性
user_count = await user_crud.count(session, email="<EMAIL>")
if user_count > 0:  # 低效

# ✅ 使用 limit 限制结果集
recent_users = await user_crud.select_models(
    session,
    created_at__gte=datetime.now() - timedelta(days=7),
    limit=100  # 限制结果数量
)

# ✅ 使用 offset 和 limit 实现分页
def paginate_users(page: int = 1, page_size: int = 20):
    offset = (page - 1) * page_size
    return user_crud.select_models(
        session,
        offset=offset,
        limit=page_size
    )
```

## 📈 监控和分析

### 1. 查询性能监控

```python
import time
import logging
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@asynccontextmanager
async def monitor_query(operation_name: str, threshold: float = 1.0):
    """查询性能监控上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        if duration > threshold:
            logger.warning(f"慢查询警告: {operation_name} 耗时 {duration:.3f}s")
        else:
            logger.debug(f"查询完成: {operation_name} 耗时 {duration:.3f}s")

# 使用示例
async def get_user_dashboard_data(session: AsyncSession, user_id: int):
    async with monitor_query("获取用户仪表板数据", threshold=0.5):
        user = await user_crud.select_model(
            session,
            pk=user_id,
            load_strategies={
                'posts': 'selectinload',
                'profile': 'joinedload'
            }
        )
    return user
```

### 2. SQL 查询日志分析

```python
# 开发环境启用 SQL 日志
engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # 打印所有 SQL 语句
    echo_pool=True,  # 打印连接池信息
)

# 生产环境使用结构化日志
import structlog

logger = structlog.get_logger()

async def log_query_stats(session: AsyncSession, operation: str):
    """记录查询统计信息"""
    info = await session.info
    logger.info(
        "数据库操作完成",
        operation=operation,
        connection_info=str(info)
    )
```

## 🎛️ 配置优化

### 1. 连接池配置

```python
from sqlalchemy.ext.asyncio import create_async_engine

# 生产环境连接池配置
engine = create_async_engine(
    DATABASE_URL,
    # 连接池大小
    pool_size=20,
    # 最大溢出连接数
    max_overflow=30,
    # 连接池超时时间
    pool_timeout=30,
    # 连接回收时间（秒）
    pool_recycle=3600,
    # 连接预检查
    pool_pre_ping=True,
    # 查询超时
    query_timeout=30,
)
```

### 2. 会话配置

```python
from sqlalchemy.ext.asyncio import async_sessionmaker

# 优化的会话配置
async_session = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    # 提交后不过期对象
    expire_on_commit=False,
    # 自动刷新
    autoflush=True,
    # 自动提交（通常设为 False）
    autocommit=False,
)
```

## 📊 性能测试

### 1. 基准测试

```python
import asyncio
import time
from typing import List

async def benchmark_crud_operations():
    """CRUD 操作基准测试"""
    
    # 测试批量创建
    start_time = time.time()
    users_data = [
        UserCreate(name=f"用户{i}", email=f"user{i}@example.com")
        for i in range(1000)
    ]
    
    async with db_manager.get_session() as session:
        users = await user_crud.create_models(session, users_data)
    
    create_time = time.time() - start_time
    print(f"批量创建 1000 用户耗时: {create_time:.3f}s")
    
    # 测试批量查询
    start_time = time.time()
    async with db_manager.get_session() as session:
        users = await user_crud.select_models(
            session,
            load_strategies=['posts', 'profile'],
            limit=100
        )
    
    query_time = time.time() - start_time
    print(f"查询 100 用户（含关系）耗时: {query_time:.3f}s")

# 运行基准测试
asyncio.run(benchmark_crud_operations())
```

### 2. 内存使用监控

```python
import psutil
import os

def monitor_memory_usage():
    """监控内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss': memory_info.rss / 1024 / 1024,  # MB
        'vms': memory_info.vms / 1024 / 1024,  # MB
        'percent': process.memory_percent()
    }

# 在关键操作前后监控内存
async def memory_intensive_operation():
    before = monitor_memory_usage()
    
    # 执行大量数据操作
    async with db_manager.get_session() as session:
        users = await user_crud.select_models(session, limit=10000)
    
    after = monitor_memory_usage()
    
    print(f"内存使用变化: {after['rss'] - before['rss']:.2f} MB")
```

通过遵循这些性能优化策略，您可以确保 SQLAlchemy CRUD Plus 在生产环境中高效运行。

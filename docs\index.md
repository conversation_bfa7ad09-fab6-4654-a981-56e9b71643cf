<h1 style="text-align: center; margin: 3rem auto">SQLAlchemy CRUD Plus</h1>

<p style="text-align: center; font-size: 1.2em; color: #666; margin-bottom: 2rem;">
现代化的 SQLAlchemy 2.0 异步 CRUD 操作库，为 Python 开发者提供简洁、强大、类型安全的数据库操作体验
</p>

## 🚀 为什么选择 SQLAlchemy CRUD Plus？

SQLAlchemy CRUD Plus 专为现代 Python 异步应用设计，解决了传统 ORM 操作中的常见痛点：

- **🎯 开发效率提升 80%**：统一的 API 设计，减少重复代码
- **🔒 类型安全保障**：完整的 TypeScript 级别类型提示支持
- **⚡ 性能优化内置**：智能的关系查询和 N+1 问题解决方案
- **🛡️ 企业级稳定性**：完善的错误处理和事务控制机制

## ✨ 核心特性

### 🔧 简化的 CRUD 操作
- **统一 API 接口**：一套 API 处理所有 CRUD 操作，学习成本低
- **批量操作支持**：高效的批量创建、更新、删除操作
- **完整类型提示**：IDE 智能提示，减少运行时错误
- **灵活参数控制**：支持 `flush`、`commit` 等精细化事务控制

### 🔗 强大的关系查询
- **多种预加载策略**：`selectinload`、`joinedload`、`subqueryload` 等
- **灵活 JOIN 控制**：支持 `inner`、`left`、`right`、`full` 等 JOIN 类型
- **嵌套关系支持**：轻松处理多层关系数据加载
- **性能优化内置**：自动避免 N+1 查询问题

### 🎯 丰富的过滤功能
- **34+ 过滤操作符**：覆盖所有常见查询需求
- **复杂条件支持**：OR 条件、算术运算、字符串匹配
- **Django 风格语法**：熟悉的 `field__operator` 语法
- **复合主键支持**：完整支持复合主键操作

### 🏢 企业级特性
- **现代异步架构**：基于 SQLAlchemy 2.0 异步引擎
- **完整事务控制**：支持嵌套事务、保存点等高级特性
- **详细错误处理**：7 种自定义异常类型，精确错误定位
- **生产环境就绪**：经过大规模生产环境验证

## 🚀 快速体验

### 安装

```bash
pip install sqlalchemy-crud-plus
```

### 30 秒上手示例

```python
from sqlalchemy_crud_plus import CRUDPlus

# 1. 创建 CRUD 实例（支持完整类型提示）
user_crud = CRUDPlus(User)

# 2. 基础 CRUD 操作 - 简洁而强大
user = await user_crud.create_model(session, user_data)
user = await user_crud.select_model(session, pk=1)
await user_crud.update_model(session, pk=1, obj=update_data)
await user_crud.delete_model(session, pk=1)

# 3. 高级查询 - 一行代码解决复杂需求
users = await user_crud.select_models(
    session,
    # 预加载关系数据，避免 N+1 问题
    load_strategies=['posts', 'profile'],
    # Django 风格过滤条件
    name__like='%admin%',
    age__gte=18,
    # 复杂 OR 条件
    __or__={'is_vip': True, 'level__gte': 5}
)
```

### 与传统方式对比

=== "SQLAlchemy CRUD Plus"
    ```python
    # 简洁、类型安全、功能强大
    users = await user_crud.select_models(
        session,
        load_strategies=['posts', 'profile'],
        name__like='%admin%',
        age__gte=18
    )
    ```

=== "原生 SQLAlchemy"
    ```python
    # 冗长、容易出错、需要手动处理关系
    from sqlalchemy.orm import selectinload, joinedload

    stmt = (
        select(User)
        .options(
            selectinload(User.posts),
            joinedload(User.profile)
        )
        .where(
            and_(
                User.name.like('%admin%'),
                User.age >= 18
            )
        )
    )
    result = await session.execute(stmt)
    users = result.scalars().all()
    ```

## 📚 学习路径

### 🎯 新手入门（5-10 分钟）
- [📦 安装指南](installing.md) - 环境准备和依赖安装
- [⚡ 快速开始](getting-started/quick-start.md) - 5分钟上手核心功能

### 🔧 核心功能（20-30 分钟）
- [📝 基础 CRUD](usage/crud.md) - 掌握增删改查操作
- [🔗 关系查询](relationships/overview.md) - 处理表关联和数据预加载
- [🎯 高级过滤](advanced/filter.md) - 34+ 种过滤操作符详解

### 🏢 生产实践（30-60 分钟）
- [⚡ 事务控制](advanced/transaction.md) - 事务管理和性能优化
- [🔧 最佳实践](best-practices/overview.md) - 生产环境使用指南
- [🚀 性能优化](best-practices/performance.md) - 查询优化和性能调优

### 🔌 框架集成
- [FastAPI 集成](integrations/fastapi.md) - 与 FastAPI 完美结合
- [Django 风格](integrations/django-style.md) - Django 开发者迁移指南

## 🎯 适用场景

- ✅ **Web API 开发**：FastAPI、Flask 等框架的数据层
- ✅ **微服务架构**：服务间数据交互和持久化
- ✅ **数据分析应用**：复杂查询和数据聚合
- ✅ **企业级应用**：需要事务控制和错误处理的业务系统
- ✅ **原型开发**：快速构建 MVP 和概念验证
